#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste para verificar consistência da rede de coautoria com a versão anterior.
"""

import pandas as pd
import networkx as nx
from collections import Counter, defaultdict
from itertools import combinations

def test_consistency():
    """Testa se a nova versão mantém consistência com a anterior."""
    print("🔍 TESTANDO CONSISTÊNCIA DA REDE DE COAUTORIA")
    print("=" * 60)
    
    try:
        # Carregar dados
        df = pd.read_csv('dataset_llm_ehr_curado.csv')
        df['year'] = pd.to_numeric(df['year'], errors='coerce')
        df = df[(df['year'] >= 2020) & (df['year'] <= 2024)]
        
        print(f"✅ Dataset carregado: {len(df)} registros")
        
        # === VERSÃO ANTERIOR (ORIGINAL) ===
        print("\n📊 VERSÃO ANTERIOR (ORIGINAL):")
        
        edges_old = []
        for authors_row in df['authors'].dropna():
            authors_list = [a.strip() for a in str(authors_row).split(';')
                           if len(a.strip()) > 2]
            if len(authors_list) > 1:
                edges_old.extend(combinations(authors_list, 2))
        
        G_old = nx.Graph()
        G_old.add_edges_from(edges_old)
        
        # Filtrar por grau mínimo (versão anterior)
        degree_dict_old = dict(G_old.degree())
        relevant_nodes_old = [node for node, degree in degree_dict_old.items() if degree >= 2]
        G_filtered_old = G_old.subgraph(relevant_nodes_old[:30])
        
        print(f"   - Nós: {len(G_filtered_old.nodes())}")
        print(f"   - Arestas: {len(G_filtered_old.edges())}")
        print(f"   - Densidade: {nx.density(G_filtered_old):.3f}")
        
        # Mostrar alguns autores da versão anterior
        old_authors = list(G_filtered_old.nodes())[:10]
        print(f"   - Primeiros 10 autores: {old_authors}")
        
        # === NOVA VERSÃO (AJUSTÁVEL COM PARÂMETROS PADRÃO) ===
        print("\n📊 NOVA VERSÃO (min_collaborations=2, max_authors=30):")
        
        edges_new = []
        author_papers = defaultdict(list)
        
        for idx, authors_row in df['authors'].dropna().items():
            authors_list = [a.strip() for a in str(authors_row).split(';')
                           if len(a.strip()) > 2]
            
            for author in authors_list:
                author_papers[author].append(idx)
            
            if len(authors_list) > 1:
                edges_new.extend(combinations(authors_list, 2))
        
        edge_counts = Counter(edges_new)
        
        # Criar grafo inicial
        G_initial = nx.Graph()
        G_initial.add_edges_from(edges_new)
        
        # Filtrar autores por grau mínimo (nova lógica)
        degree_dict_new = dict(G_initial.degree())
        relevant_authors = [author for author, degree in degree_dict_new.items() if degree >= 2]
        
        # Limitar ao número máximo de autores
        if len(relevant_authors) > 30:
            relevant_authors = relevant_authors[:30]
        
        G_filtered_new = G_initial.subgraph(relevant_authors)
        
        print(f"   - Nós: {len(G_filtered_new.nodes())}")
        print(f"   - Arestas: {len(G_filtered_new.edges())}")
        print(f"   - Densidade: {nx.density(G_filtered_new):.3f}")
        
        # Mostrar alguns autores da nova versão
        new_authors = list(G_filtered_new.nodes())[:10]
        print(f"   - Primeiros 10 autores: {new_authors}")
        
        # === COMPARAÇÃO ===
        print("\n🔍 COMPARAÇÃO:")
        
        # Autores em comum
        common_authors = set(G_filtered_old.nodes()) & set(G_filtered_new.nodes())
        print(f"   - Autores em comum: {len(common_authors)}/{len(G_filtered_old.nodes())}")
        
        # Diferenças
        only_old = set(G_filtered_old.nodes()) - set(G_filtered_new.nodes())
        only_new = set(G_filtered_new.nodes()) - set(G_filtered_old.nodes())
        
        if only_old:
            print(f"   - Apenas na versão anterior: {len(only_old)}")
            print(f"     Exemplos: {list(only_old)[:5]}")
        
        if only_new:
            print(f"   - Apenas na nova versão: {len(only_new)}")
            print(f"     Exemplos: {list(only_new)[:5]}")
        
        # Verificar se há sobreposição significativa
        overlap_percentage = len(common_authors) / len(G_filtered_old.nodes()) * 100
        print(f"   - Sobreposição: {overlap_percentage:.1f}%")
        
        if overlap_percentage >= 70:
            print("   ✅ ALTA CONSISTÊNCIA com a versão anterior")
        elif overlap_percentage >= 50:
            print("   ⚠️ CONSISTÊNCIA MODERADA com a versão anterior")
        else:
            print("   ❌ BAIXA CONSISTÊNCIA com a versão anterior")
        
        return overlap_percentage >= 50
        
    except Exception as e:
        print(f"❌ Erro no teste de consistência: {e}")
        return False

def main():
    """Função principal do teste."""
    success = test_consistency()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ TESTE DE CONSISTÊNCIA PASSOU!")
        print("🎯 A nova versão mantém boa consistência com a anterior")
    else:
        print("⚠️ TESTE DE CONSISTÊNCIA FALHOU")
        print("🔧 Pode ser necessário ajustar a lógica de seleção")

if __name__ == "__main__":
    main()
