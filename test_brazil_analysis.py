#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste específico para a análise do Brasil no app Streamlit.
"""

import pandas as pd
from collections import defaultdict

def test_brazil_analysis():
    """Testa a análise específica do Brasil."""
    print("🇧🇷 TESTANDO ANÁLISE DO BRASIL")
    print("=" * 50)
    
    try:
        # Carregar dados
        df = pd.read_csv('dataset_llm_ehr_curado.csv')
        df['year'] = pd.to_numeric(df['year'], errors='coerce')
        df = df[(df['year'] >= 2020) & (df['year'] <= 2024)]
        
        print(f"✅ Dataset carregado: {len(df)} registros")
        
        # Verificar se há dados de países
        if 'country_affiliation' not in df.columns:
            print("❌ Coluna 'country_affiliation' não encontrada")
            return
        
        # Filtrar artigos com afiliação do Brasil
        brazil_articles = []
        brazil_collaborations = []
        
        for _, row in df.iterrows():
            countries_str = str(row['country_affiliation'])
            if countries_str and countries_str not in ['nan', '']:
                countries = [c.strip() for c in countries_str.split(';') if c.strip()]
                # Verificar se Brasil está na lista de países
                if any(country in ['Brazil', 'BR', 'brasil', 'BRAZIL'] for country in countries):
                    brazil_articles.append(row)
                    # Verificar se é colaboração internacional
                    if len(set(countries)) > 1:
                        brazil_collaborations.append(row)
        
        if not brazil_articles:
            print("⚠️ Não foram encontrados artigos com afiliação brasileira")
            return
        
        # Converter para DataFrame
        df_brazil = pd.DataFrame(brazil_articles)
        df_brazil_collab = pd.DataFrame(brazil_collaborations)
        
        print(f"✅ Artigos brasileiros encontrados: {len(df_brazil)}")
        print(f"✅ Colaborações internacionais: {len(df_brazil_collab)}")
        
        # Calcular métricas
        collab_rate = (len(df_brazil_collab) / len(df_brazil) * 100) if len(df_brazil) > 0 else 0
        brazil_share = (len(df_brazil) / len(df) * 100) if len(df) > 0 else 0
        
        print(f"📊 Taxa de colaboração internacional: {collab_rate:.1f}%")
        print(f"📊 Participação do Brasil no dataset: {brazil_share:.1f}%")
        
        # Análise por ano
        if 'year' in df_brazil.columns:
            brazil_yearly = df_brazil['year'].value_counts().sort_index()
            print(f"\n📅 Evolução temporal do Brasil:")
            for year, count in brazil_yearly.items():
                print(f"   {int(year)}: {count} artigos")
        
        # Análise por periódicos
        if 'journal' in df_brazil.columns:
            brazil_journals = df_brazil['journal'].value_counts().head(5)
            print(f"\n📰 Top 5 periódicos com artigos brasileiros:")
            for i, (journal, count) in enumerate(brazil_journals.items(), 1):
                print(f"   {i}. {journal[:40]}: {count} artigos")
        
        # Análise de colaborações por país
        if len(df_brazil_collab) > 0:
            collaboration_countries = defaultdict(int)
            for _, row in df_brazil_collab.iterrows():
                countries_str = str(row['country_affiliation'])
                if countries_str and countries_str not in ['nan', '']:
                    countries = [c.strip() for c in countries_str.split(';') if c.strip()]
                    # Contar países parceiros (excluindo Brasil)
                    for country in countries:
                        if country not in ['Brazil', 'BR', 'brasil', 'BRAZIL']:
                            collaboration_countries[country] += 1
            
            if collaboration_countries:
                top_collaborations = sorted(collaboration_countries.items(), key=lambda x: x[1], reverse=True)[:5]
                print(f"\n🤝 Top 5 países colaboradores com o Brasil:")
                for i, (country, count) in enumerate(top_collaborations, 1):
                    print(f"   {i}. {country}: {count} colaborações")
        
        # Análise de citações
        if 'citations' in df_brazil.columns:
            total_citations_brazil = df_brazil['citations'].sum()
            avg_citations_brazil = df_brazil['citations'].mean()
            global_avg = df['citations'].mean()
            
            print(f"\n📚 Impacto dos artigos brasileiros:")
            print(f"   Total de citações: {total_citations_brazil}")
            print(f"   Média de citações: {avg_citations_brazil:.2f}")
            print(f"   Média global: {global_avg:.2f}")
            
            if global_avg > 0:
                comparison = ((avg_citations_brazil - global_avg) / global_avg * 100)
                print(f"   Comparação com média global: {comparison:+.1f}%")
        
        print(f"\n✅ ANÁLISE DO BRASIL CONCLUÍDA COM SUCESSO")
        return df_brazil
        
    except Exception as e:
        print(f"❌ Erro na análise do Brasil: {e}")
        return pd.DataFrame()

def main():
    """Função principal do teste."""
    df_brazil = test_brazil_analysis()
    
    if not df_brazil.empty:
        print(f"\n🚀 A análise do Brasil está pronta para uso no Streamlit!")
        print(f"📊 {len(df_brazil)} artigos brasileiros identificados")
    else:
        print(f"\n❌ Problemas na análise do Brasil")

if __name__ == "__main__":
    main()
