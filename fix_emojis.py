#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para corrigir os emojis corrompidos no arquivo streamlit_bibliometric_app.py
"""

def fix_emojis():
    """Corrige os emojis corrompidos no arquivo."""
    
    # Ler o arquivo
    with open('streamlit_bibliometric_app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Substituições necessárias
    replacements = {
        '"� Rede de Coocorrência"': '"🔗 Rede de Coocorrência"',
        '"�📚 Análise de Citações"': '"📚 Análise de Citações"'
    }
    
    # Aplicar substituições
    for old, new in replacements.items():
        content = content.replace(old, new)
        print(f"✅ Substituído: {old} → {new}")
    
    # Escrever o arquivo corrigido
    with open('streamlit_bibliometric_app.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Emojis corrigidos com sucesso!")

if __name__ == "__main__":
    fix_emojis()
