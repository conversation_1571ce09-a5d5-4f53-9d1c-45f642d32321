#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste específico para a nova rede de coautoria ajustável.
"""

import pandas as pd
import networkx as nx
from collections import Counter, defaultdict
from itertools import combinations

def test_adjustable_coauthorship():
    """Testa a rede de coautoria ajustável."""
    print("🕸️ TESTANDO REDE DE COAUTORIA AJUSTÁVEL")
    print("=" * 60)
    
    try:
        # Carregar dados
        df = pd.read_csv('dataset_llm_ehr_curado.csv')
        df['year'] = pd.to_numeric(df['year'], errors='coerce')
        df = df[(df['year'] >= 2020) & (df['year'] <= 2024)]
        
        print(f"✅ Dataset carregado: {len(df)} registros")
        
        # Extrair colaborações entre autores
        edges = []
        author_papers = defaultdict(list)
        
        for idx, authors_row in df['authors'].dropna().items():
            authors_list = [a.strip() for a in str(authors_row).split(';')
                           if len(a.strip()) > 2]
            
            # Armazenar quais papers cada autor participou
            for author in authors_list:
                author_papers[author].append(idx)
            
            # Criar arestas entre coautores
            if len(authors_list) > 1:
                edges.extend(combinations(authors_list, 2))
        
        if not edges:
            print("❌ Nenhuma colaboração encontrada")
            return False
        
        print(f"✅ Colaborações extraídas: {len(edges)} pares de autores")
        
        # Contar colaborações
        edge_counts = Counter(edges)
        print(f"✅ Colaborações únicas: {len(edge_counts)}")
        
        # Testar diferentes configurações
        configurations = [
            {"min_collab": 1, "max_authors": 30},
            {"min_collab": 2, "max_authors": 50},
            {"min_collab": 3, "max_authors": 40},
        ]
        
        for i, config in enumerate(configurations, 1):
            print(f"\n📊 Configuração {i}: min_collab={config['min_collab']}, max_authors={config['max_authors']}")
            
            # Filtrar por número mínimo de colaborações
            filtered_edges = [(author1, author2, count) for (author1, author2), count in edge_counts.items() 
                             if count >= config['min_collab']]
            
            if not filtered_edges:
                print(f"   ⚠️ Nenhuma colaboração com mínimo de {config['min_collab']}")
                continue
            
            # Criar grafo
            G = nx.Graph()
            for author1, author2, weight in filtered_edges:
                G.add_edge(author1, author2, weight=weight)
            
            # Filtrar autores mais produtivos
            degree_dict = dict(G.degree())
            top_authors = sorted(degree_dict.items(), key=lambda x: x[1], reverse=True)[:config['max_authors']]
            top_author_names = [author for author, _ in top_authors]
            G_filtered = G.subgraph(top_author_names)
            
            print(f"   ✅ Rede criada:")
            print(f"      - Nós (autores): {len(G_filtered.nodes())}")
            print(f"      - Arestas (colaborações): {len(G_filtered.edges())}")
            print(f"      - Densidade: {nx.density(G_filtered):.3f}")
            
            if len(G_filtered.nodes()) > 0:
                avg_degree = sum(dict(G_filtered.degree()).values()) / len(G_filtered.nodes())
                print(f"      - Grau médio: {avg_degree:.1f}")
                
                # Top colaborações
                top_collaborations = sorted(filtered_edges, key=lambda x: x[2], reverse=True)[:3]
                print(f"   🤝 Top 3 colaborações:")
                for j, (author1, author2, weight) in enumerate(top_collaborations, 1):
                    author1_short = author1[:20] + "..." if len(author1) > 20 else author1
                    author2_short = author2[:20] + "..." if len(author2) > 20 else author2
                    print(f"      {j}. {author1_short} ↔ {author2_short}: {weight}")
                
                # Autores mais centrais
                if len(G_filtered.nodes()) > 1:
                    centrality = nx.degree_centrality(G_filtered)
                    top_central = sorted(centrality.items(), key=lambda x: x[1], reverse=True)[:3]
                    print(f"   🏆 Top 3 autores mais centrais:")
                    for j, (author, cent) in enumerate(top_central, 1):
                        author_short = author[:30] + "..." if len(author) > 30 else author
                        print(f"      {j}. {author_short}: {cent:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na análise de coautoria: {e}")
        return False

def main():
    """Função principal do teste."""
    print("🚀 TESTANDO REDE DE COAUTORIA AJUSTÁVEL")
    print("=" * 60)
    
    success = test_adjustable_coauthorship()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ REDE DE COAUTORIA AJUSTÁVEL FUNCIONANDO!")
        print("🎯 Recursos implementados:")
        print("   - Parâmetros configuráveis (colaborações mínimas e máximo de autores)")
        print("   - Visualização interativa com Plotly")
        print("   - Métricas de rede (densidade, grau médio, centralidade)")
        print("   - Rankings de colaborações e autores centrais")
        print("   - Tamanhos proporcionais e espessuras de linha")
        print("\n🚀 Execute: streamlit run streamlit_bibliometric_app.py")
    else:
        print("❌ PROBLEMAS NA REDE DE COAUTORIA")

if __name__ == "__main__":
    main()
