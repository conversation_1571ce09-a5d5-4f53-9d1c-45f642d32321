#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de teste para verificar se o app Streamlit pode carregar os dados corretamente.
"""

import pandas as pd
import numpy as np
from collections import Counter, defaultdict
import re

def test_data_loading():
    """Testa o carregamento dos dados."""
    print("🔍 Testando carregamento dos dados...")
    
    try:
        df = pd.read_csv('dataset_llm_ehr_curado.csv')
        print(f"✅ Dados carregados com sucesso: {len(df)} registros")
        
        # Converter ano para numérico
        df['year'] = pd.to_numeric(df['year'], errors='coerce')
        
        # Filtrar anos válidos
        df_filtered = df[(df['year'] >= 2020) & (df['year'] <= 2024)]
        print(f"✅ Dados filtrados (2020-2024): {len(df_filtered)} registros")
        
        return df_filtered
        
    except Exception as e:
        print(f"❌ Erro ao carregar dados: {e}")
        return pd.DataFrame()

def test_basic_analyses(df):
    """Testa análises básicas."""
    print("\n📊 Testando análises básicas...")
    
    if df.empty:
        print("❌ DataFrame vazio, não é possível executar análises")
        return
    
    # Teste 1: Evolução temporal
    try:
        yearly_production = df['year'].value_counts().sort_index()
        print(f"✅ Evolução temporal: {len(yearly_production)} anos analisados")
        print(f"   Período: {yearly_production.index.min():.0f}-{yearly_production.index.max():.0f}")
    except Exception as e:
        print(f"❌ Erro na análise temporal: {e}")
    
    # Teste 2: Análise de autores
    try:
        all_authors = []
        for authors_row in df['authors'].dropna():
            individual_authors = [a.strip() for a in str(authors_row).split(';')
                                if len(a.strip()) > 2]
            all_authors.extend(individual_authors)
        
        author_counts = Counter(all_authors)
        print(f"✅ Análise de autores: {len(author_counts)} autores únicos")
        print(f"   Autor mais produtivo: {author_counts.most_common(1)[0] if author_counts else 'N/A'}")
    except Exception as e:
        print(f"❌ Erro na análise de autores: {e}")
    
    # Teste 3: Análise de periódicos
    try:
        journal_counts = df['journal'].value_counts()
        print(f"✅ Análise de periódicos: {len(journal_counts)} periódicos únicos")
        print(f"   Periódico principal: {journal_counts.index[0] if len(journal_counts) > 0 else 'N/A'}")
    except Exception as e:
        print(f"❌ Erro na análise de periódicos: {e}")
    
    # Teste 4: Análise de palavras-chave
    try:
        all_keywords = []
        for keywords_row in df['keywords'].dropna():
            if str(keywords_row).lower() not in ['nan', 'none', '']:
                individual_keywords = re.split(r'[;,]', str(keywords_row))
                clean_keywords = [
                    k.strip().lower().title()
                    for k in individual_keywords
                    if len(k.strip()) > 3 and not k.strip().isdigit()
                ]
                all_keywords.extend(clean_keywords)
        
        keyword_counts = Counter(all_keywords)
        print(f"✅ Análise de palavras-chave: {len(keyword_counts)} palavras-chave únicas")
        print(f"   Palavra-chave principal: {keyword_counts.most_common(1)[0] if keyword_counts else 'N/A'}")
    except Exception as e:
        print(f"❌ Erro na análise de palavras-chave: {e}")
    
    # Teste 5: Análise de citações
    try:
        if 'citations' in df.columns:
            total_citations = df['citations'].sum()
            avg_citations = df['citations'].mean()
            print(f"✅ Análise de citações: {total_citations} citações totais")
            print(f"   Média de citações: {avg_citations:.2f}")
        else:
            print("⚠️ Coluna 'citations' não encontrada")
    except Exception as e:
        print(f"❌ Erro na análise de citações: {e}")
    
    # Teste 6: Análise de países
    try:
        if 'country_affiliation' in df.columns:
            country_data = df['country_affiliation'].dropna()
            countries_with_data = len(country_data)
            print(f"✅ Análise de países: {countries_with_data} registros com dados de país")
        else:
            print("⚠️ Coluna 'country_affiliation' não encontrada")
    except Exception as e:
        print(f"❌ Erro na análise de países: {e}")

def test_data_quality(df):
    """Testa a qualidade dos dados."""
    print("\n🔍 Testando qualidade dos dados...")
    
    if df.empty:
        print("❌ DataFrame vazio")
        return
    
    # Verificar colunas essenciais
    essential_columns = ['title', 'authors', 'journal', 'year']
    missing_columns = [col for col in essential_columns if col not in df.columns]
    
    if missing_columns:
        print(f"❌ Colunas essenciais ausentes: {missing_columns}")
    else:
        print("✅ Todas as colunas essenciais estão presentes")
    
    # Verificar dados faltantes
    print("\n📊 Dados faltantes por coluna:")
    for col in df.columns:
        missing_count = df[col].isna().sum()
        missing_pct = (missing_count / len(df)) * 100
        status = "✅" if missing_pct < 10 else "⚠️" if missing_pct < 50 else "❌"
        print(f"   {status} {col}: {missing_count} ({missing_pct:.1f}%)")
    
    # Verificar duplicatas
    duplicates = df.duplicated(subset=['title']).sum()
    print(f"\n📋 Duplicatas (por título): {duplicates}")
    
    # Verificar distribuição por ano
    print(f"\n📅 Distribuição por ano:")
    yearly_dist = df['year'].value_counts().sort_index()
    for year, count in yearly_dist.items():
        print(f"   {int(year)}: {count} artigos")

def main():
    """Função principal do teste."""
    print("🚀 INICIANDO TESTES DO APP STREAMLIT")
    print("=" * 50)
    
    # Carregar dados
    df = test_data_loading()
    
    # Executar testes
    test_basic_analyses(df)
    test_data_quality(df)
    
    print("\n" + "=" * 50)
    print("✅ TESTES CONCLUÍDOS")
    
    if not df.empty:
        print(f"📊 Dataset pronto para uso com {len(df)} registros")
        print("🚀 Execute: streamlit run streamlit_bibliometric_app.py")
    else:
        print("❌ Problemas encontrados no dataset")

if __name__ == "__main__":
    main()
