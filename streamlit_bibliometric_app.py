#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Streamlit App para Análise Bibliométrica: LLMs em Sistemas de Recuperação de Informação em Saúde
Baseado no código bibliometric_analysis_enhanced.py
"""

import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import networkx as nx
from collections import Counter, defaultdict
from itertools import combinations
import re
from wordcloud import WordCloud
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
from sklearn.linear_model import LinearRegression
import warnings

warnings.filterwarnings("ignore")

# Configuração da página
st.set_page_config(
    page_title="Análise Bibliométrica - LLMs em EHR",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Configuração de estilo
sns.set_style("whitegrid")
plt.rcParams.update({
    'figure.figsize': (12, 8),
    'font.size': 11,
    'axes.titlesize': 14,
    'axes.titleweight': 'bold'
})

@st.cache_data
def load_data():
    """Carrega e processa os dados do dataset."""
    try:
        df = pd.read_csv('dataset_llm_ehr_curado.csv')
        # Converter ano para numérico
        df['year'] = pd.to_numeric(df['year'], errors='coerce')
        # Filtrar anos válidos
        df = df[(df['year'] >= 2020) & (df['year'] <= 2024)]
        return df
    except Exception as e:
        st.error(f"Erro ao carregar dados: {e}")
        return pd.DataFrame()

def display_dataset_overview(df):
    """Exibe visão geral do dataset."""
    st.header("📊 Visão Geral do Dataset")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total de Artigos", len(df))
    
    with col2:
        st.metric("Período", f"{df['year'].min():.0f}-{df['year'].max():.0f}")
    
    with col3:
        unique_journals = df['journal'].nunique()
        st.metric("Periódicos Únicos", unique_journals)
    
    with col4:
        total_citations = df['citations'].sum() if 'citations' in df.columns else 0
        st.metric("Total de Citações", total_citations)
    
    # Mostrar amostra dos dados - apenas OpenAlex
    st.subheader("📋 Amostra dos Dados (OpenAlex)")
    df_openalex = df[df['source'] == 'OpenAlex']
    if len(df_openalex) > 0:
        st.dataframe(df_openalex.head(10))
    else:
        st.warning("Nenhum dado do OpenAlex encontrado no dataset")

def analyze_temporal_evolution(df):
    """Análise da evolução temporal."""
    st.header("📈 Evolução Temporal da Produção Científica")
    
    yearly_production = df['year'].value_counts().sort_index()
    
    # Gráfico interativo com Plotly
    fig = px.bar(
        x=yearly_production.index,
        y=yearly_production.values,
        title='Evolução da Produção Científica sobre LLMs em EHR (2020-2024)',
        labels={'x': 'Ano de Publicação', 'y': 'Número de Publicações'},
        color=yearly_production.values,
        color_continuous_scale='viridis'
    )
    
    fig.update_layout(
        xaxis_title="Ano de Publicação",
        yaxis_title="Número de Publicações",
        showlegend=False
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # Calcular taxa de crescimento
    if len(yearly_production) > 1:
        growth_rate = ((yearly_production.iloc[-1] - yearly_production.iloc[0]) /
                      yearly_production.iloc[0] * 100)
        st.info(f"📈 Taxa de crescimento 2020-2024: {growth_rate:.1f}%")
    
    # Mostrar dados tabulares
    st.subheader("📊 Dados por Ano")
    yearly_df = pd.DataFrame({
        'Ano': yearly_production.index,
        'Publicações': yearly_production.values
    })
    st.dataframe(yearly_df)

def analyze_top_authors(df, top_n=15):
    """Análise dos autores mais produtivos."""
    st.header("👥 Autores Mais Produtivos")
    
    all_authors = []
    for authors_row in df['authors'].dropna():
        individual_authors = [a.strip() for a in str(authors_row).split(';')
                            if len(a.strip()) > 2]
        all_authors.extend(individual_authors)
    
    if not all_authors:
        st.warning("Dados de autores não disponíveis")
        return
    
    author_counts = Counter(all_authors)
    top_authors = author_counts.most_common(top_n)
    
    df_authors = pd.DataFrame(top_authors, columns=['Autor', 'Publicações'])
    
    # Gráfico interativo
    fig = px.bar(
        df_authors,
        x='Publicações',
        y='Autor',
        orientation='h',
        title=f'Top {top_n} Autores Mais Produtivos',
        color='Publicações',
        color_continuous_scale='rdylbu'
    )
    
    fig.update_layout(
        yaxis={'categoryorder': 'total ascending'},
        height=600
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # Mostrar tabela
    st.subheader("📊 Ranking de Autores")
    st.dataframe(df_authors)

def analyze_journals(df, top_n=10):
    """Análise dos periódicos mais relevantes."""
    st.header("📰 Periódicos Mais Relevantes")
    
    journal_counts = df['journal'].value_counts().head(top_n)
    
    # Gráfico interativo
    fig = px.bar(
        x=journal_counts.values,
        y=journal_counts.index,
        orientation='h',
        title=f'Top {top_n} Periódicos com Maior Concentração de Artigos',
        labels={'x': 'Número de Artigos', 'y': 'Periódico'},
        color=journal_counts.values,
        color_continuous_scale='blues'
    )
    
    fig.update_layout(
        yaxis={'categoryorder': 'total ascending'},
        height=600
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # Mostrar tabela
    st.subheader("📊 Ranking de Periódicos")
    journal_df = pd.DataFrame({
        'Periódico': journal_counts.index,
        'Artigos': journal_counts.values
    })
    st.dataframe(journal_df)

def analyze_keywords(df, top_n=25):
    """Análise de palavras-chave."""
    st.header("🔤 Análise de Palavras-Chave")
    
    all_keywords = []
    for keywords_row in df['keywords'].dropna():
        if str(keywords_row).lower() not in ['nan', 'none', '']:
            individual_keywords = re.split(r'[;,]', str(keywords_row))
            clean_keywords = [
                k.strip().lower().title()
                for k in individual_keywords
                if len(k.strip()) > 3 and not k.strip().isdigit()
            ]
            all_keywords.extend(clean_keywords)
    
    if not all_keywords:
        st.warning("Dados de palavras-chave não disponíveis")
        return
    
    keyword_counts = Counter(all_keywords)
    top_keywords = keyword_counts.most_common(top_n)
    
    df_keywords = pd.DataFrame(top_keywords, columns=['Palavra-chave', 'Frequência'])
    
    # Gráfico de barras
    fig = px.bar(
        df_keywords.head(15),
        x='Frequência',
        y='Palavra-chave',
        orientation='h',
        title='Top 15 Palavras-Chave Mais Frequentes',
        color='Frequência',
        color_continuous_scale='viridis'
    )
    
    fig.update_layout(
        yaxis={'categoryorder': 'total ascending'},
        height=600
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # Nuvem de palavras
    if len(all_keywords) > 10:
        st.subheader("☁️ Nuvem de Palavras-Chave")
        
        wordcloud = WordCloud(
            width=800, height=400,
            background_color='white',
            colormap='viridis',
            max_words=100,
            relative_scaling=0.5
        ).generate(' '.join(all_keywords))
        
        fig, ax = plt.subplots(figsize=(12, 6))
        ax.imshow(wordcloud, interpolation='bilinear')
        ax.axis('off')
        ax.set_title('Nuvem de Palavras-Chave', fontsize=16, fontweight='bold', pad=20)
        
        st.pyplot(fig)
    
    # Mostrar tabela
    st.subheader("📊 Ranking de Palavras-Chave")
    st.dataframe(df_keywords)

def analyze_collaboration_network(df):
    """Análise de rede de coautoria."""
    st.header("🕸️ Rede de Coautoria")

    # Parâmetros de configuração
    col1, col2 = st.columns(2)
    with col1:
        min_collaborations = st.slider("Colaborações mínimas", 1, 10, 2)
    with col2:
        max_authors = st.slider("Máximo de autores", 20, 100, 50)

    try:
        # Extrair colaborações entre autores
        edges = []
        author_papers = defaultdict(list)

        for idx, authors_row in df['authors'].dropna().items():
            authors_list = [a.strip() for a in str(authors_row).split(';')
                           if len(a.strip()) > 2]

            # Armazenar quais papers cada autor participou
            for author in authors_list:
                author_papers[author].append(idx)

            # Criar arestas entre coautores
            if len(authors_list) > 1:
                edges.extend(combinations(authors_list, 2))

        if not edges:
            st.warning("Dados insuficientes para rede de coautoria")
            return

        # Contar colaborações
        edge_counts = Counter(edges)

        # Criar grafo inicial com todas as colaborações
        G_initial = nx.Graph()
        G_initial.add_edges_from(edges)

        # Filtrar autores por grau mínimo (similar à versão anterior)
        degree_dict = dict(G_initial.degree())
        relevant_authors = [author for author, degree in degree_dict.items() if degree >= min_collaborations]

        # Limitar ao número máximo de autores (mantendo ordem similar à anterior)
        if len(relevant_authors) > max_authors:
            relevant_authors = relevant_authors[:max_authors]

        if not relevant_authors:
            st.warning(f"Nenhum autor encontrado com mínimo de {min_collaborations} colaborações")
            return

        # Criar subgrafo com autores relevantes
        G_filtered = G_initial.subgraph(relevant_authors)

        # Calcular pesos das arestas para visualização
        filtered_edges = []
        for edge in G_filtered.edges():
            weight = edge_counts.get(edge, edge_counts.get((edge[1], edge[0]), 1))
            filtered_edges.append((edge[0], edge[1], weight))

        if len(G_filtered.nodes) > 2:
            # Layout do grafo
            pos = nx.spring_layout(G_filtered, k=3, iterations=50)

            # Preparar dados para Plotly
            edge_x = []
            edge_y = []
            edge_weights = []

            for edge in G_filtered.edges():
                x0, y0 = pos[edge[0]]
                x1, y1 = pos[edge[1]]
                edge_x.extend([x0, x1, None])
                edge_y.extend([y0, y1, None])
                weight = G_filtered[edge[0]][edge[1]].get('weight', 1)
                edge_weights.append(weight)

            # Nós
            node_x = []
            node_y = []
            node_text = []
            node_size = []
            node_hover = []

            for node in G_filtered.nodes():
                x, y = pos[node]
                node_x.append(x)
                node_y.append(y)
                # Truncar nome do autor para exibição
                display_name = node[:20] + "..." if len(node) > 20 else node
                node_text.append(display_name)

                # Informações do hover
                num_papers = len(author_papers[node])
                num_collaborations = G_filtered.degree[node]
                node_hover.append(f"{node}<br>Artigos: {num_papers}<br>Colaboradores: {num_collaborations}")

                # Tamanho baseado no número de colaborações
                size = G_filtered.degree[node]
                node_size.append(min(50, max(10, size * 5)))

            # Criar gráfico
            fig = go.Figure()

            # Adicionar arestas com espessura baseada no peso
            if edge_weights:
                max_weight = max(edge_weights)
                for i in range(0, len(edge_x), 3):
                    if i//3 < len(edge_weights):
                        weight = edge_weights[i//3]
                        line_width = max(1, (weight / max_weight) * 5)
                        fig.add_trace(go.Scatter(
                            x=edge_x[i:i+2], y=edge_y[i:i+2],
                            line=dict(width=line_width, color='lightgray'),
                            hoverinfo='none',
                            mode='lines',
                            showlegend=False
                        ))

            # Adicionar nós
            fig.add_trace(go.Scatter(
                x=node_x, y=node_y,
                mode='markers+text',
                hoverinfo='text',
                text=node_text,
                textposition="middle center",
                textfont=dict(size=8),
                marker=dict(
                    size=node_size,
                    color='lightcoral',
                    line=dict(width=2, color='darkred')
                ),
                hovertext=node_hover,
                showlegend=False
            ))

            fig.update_layout(
                title=f'Rede de Coautoria (min. {min_collaborations} colaborações)',
                showlegend=False,
                hovermode='closest',
                margin=dict(b=20,l=5,r=5,t=40),
                annotations=[ dict(
                    text="Tamanho dos nós = número de colaboradores | Espessura das linhas = força da colaboração",
                    showarrow=False,
                    xref="paper", yref="paper",
                    x=0.005, y=-0.002,
                    xanchor='left', yanchor='bottom',
                    font=dict(size=10)
                )],
                xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                height=700
            )

            st.plotly_chart(fig, use_container_width=True)

            # Métricas da rede
            st.subheader("📊 Métricas da Rede")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("Nós (Autores)", len(G_filtered.nodes()))

            with col2:
                st.metric("Arestas (Colaborações)", len(G_filtered.edges()))

            with col3:
                density = nx.density(G_filtered)
                st.metric("Densidade", f"{density:.3f}")

            with col4:
                if len(G_filtered.nodes()) > 0:
                    avg_degree = sum(dict(G_filtered.degree()).values()) / len(G_filtered.nodes())
                    st.metric("Grau Médio", f"{avg_degree:.1f}")

            # Top colaborações
            st.subheader("🤝 Principais Colaborações")
            top_collaborations = sorted(filtered_edges, key=lambda x: x[2], reverse=True)[:10]

            if top_collaborations:
                collab_df = pd.DataFrame(top_collaborations, columns=['Autor 1', 'Autor 2', 'Colaborações'])
                st.dataframe(collab_df)

            # Autores mais centrais
            st.subheader("🏆 Autores Mais Centrais")
            centrality = nx.degree_centrality(G_filtered)
            top_central = sorted(centrality.items(), key=lambda x: x[1], reverse=True)[:10]

            if top_central:
                central_df = pd.DataFrame(top_central, columns=['Autor', 'Centralidade'])
                central_df['Centralidade'] = central_df['Centralidade'].round(3)
                st.dataframe(central_df)

        else:
            st.warning("Rede muito pequena para visualização")

    except Exception as e:
        st.error(f"Erro na análise de rede de coautoria: {e}")
        st.info("Certifique-se de que o NetworkX está instalado: pip install networkx")

def analyze_citations(df):
    """Análise de citações."""
    st.header("📚 Análise de Citações")

    if 'citations' not in df.columns or df['citations'].sum() == 0:
        st.warning("Dados de citação não disponíveis")
        return

    # Top artigos citados
    top_cited = df.nlargest(10, 'citations')[['title', 'authors', 'year', 'citations', 'journal']]

    st.subheader("📚 Top 10 Artigos Mais Citados")
    for i, (_, row) in enumerate(top_cited.iterrows(), 1):
        with st.expander(f"{i}. {row['citations']} citações - {row['title'][:60]}..."):
            st.write(f"**Autores:** {row['authors']}")
            st.write(f"**Periódico:** {row['journal']}")
            st.write(f"**Ano:** {row['year']}")
            st.write(f"**Citações:** {row['citations']}")

    # Distribuição de citações
    df_with_citations = df[df['citations'] > 0]
    if len(df_with_citations) > 0:
        st.subheader("📊 Distribuição de Citações")

        fig = px.histogram(
            df_with_citations,
            x='citations',
            nbins=20,
            title='Distribuição de Citações',
            labels={'citations': 'Número de Citações', 'count': 'Frequência'}
        )

        # Adicionar linha da média
        mean_citations = df_with_citations['citations'].mean()
        fig.add_vline(x=mean_citations, line_dash="dash", line_color="red",
                     annotation_text=f"Média: {mean_citations:.1f}")

        st.plotly_chart(fig, use_container_width=True)

def analyze_lotka_law(df):
    """Análise da Lei de Lotka (produtividade dos autores)."""
    st.header("📊 Lei de Lotka - Produtividade dos Autores")

    # Contar número de publicações por autor
    author_publications = defaultdict(int)
    for authors_row in df['authors'].dropna():
        authors_list = [a.strip() for a in str(authors_row).split(';')
                       if len(a.strip()) > 2]
        for author in authors_list:
            author_publications[author] += 1

    if not author_publications:
        st.warning("Dados insuficientes para análise de Lotka")
        return

    # Contar número de autores por número de publicações
    productivity_distribution = defaultdict(int)
    for author, count in author_publications.items():
        productivity_distribution[count] += 1

    # Preparar dados para gráfico
    sorted_distribution = sorted(productivity_distribution.items())
    publications_counts = [item[0] for item in sorted_distribution]
    authors_counts = [item[1] for item in sorted_distribution]

    # Criar DataFrame para Plotly
    df_lotka = pd.DataFrame({
        'Publicações por Autor': publications_counts,
        'Número de Autores': authors_counts
    })

    # Gráfico da distribuição
    fig = px.bar(
        df_lotka,
        x='Publicações por Autor',
        y='Número de Autores',
        title='Distribuição de Produtividade dos Autores (Lei de Lotka)',
        log_y=True
    )

    st.plotly_chart(fig, use_container_width=True)

    # Calcular ajuste para lei de Lotka
    if len(sorted_distribution) > 1:
        _, a1 = sorted_distribution[0]
        expected_values = [a1 / (p ** 2) for p, _ in sorted_distribution]
        actual_values = [a for _, a in sorted_distribution]

        correlation_matrix = np.corrcoef(actual_values, expected_values)
        r_squared = correlation_matrix[0, 1] ** 2

        st.info(f"🔍 Ajuste à Lei de Lotka (R²): {r_squared:.3f}")
        if r_squared > 0.7:
            st.success("✅ A distribuição segue aproximadamente a Lei de Lotka")
        else:
            st.warning("⚠️ A distribuição não segue claramente a Lei de Lotka")

    # Estatísticas
    st.subheader("📊 Estatísticas")
    col1, col2 = st.columns(2)

    with col1:
        st.metric("Autores Analisados", len(author_publications))

    with col2:
        avg_publications = np.mean(list(author_publications.values()))
        st.metric("Publicações por Autor (Média)", f"{avg_publications:.2f}")

def analyze_exponential_growth(df):
    """Análise de crescimento exponencial e modelos preditivos."""
    st.header("📈 Crescimento Exponencial e Previsões")

    yearly_production = df['year'].value_counts().sort_index()

    if len(yearly_production) < 3:
        st.warning("Dados insuficientes para análise de crescimento")
        return

    # Preparar dados para regressão
    years = np.array(yearly_production.index).reshape(-1, 1)
    publications = np.array(yearly_production.values)

    # Ajuste exponencial
    log_publications = np.log(publications)
    model = LinearRegression()
    model.fit(years, log_publications)

    # Parâmetros do modelo exponencial
    a = np.exp(model.intercept_)
    b = model.coef_[0]

    # Previsões
    future_years = np.arange(years.min(), years.max() + 6).reshape(-1, 1)
    predicted_log = model.predict(future_years)
    predicted_publications = np.exp(predicted_log)

    # Calcular R²
    r2_score = model.score(years, log_publications)

    # Criar gráfico
    df_growth = pd.DataFrame({
        'Ano': future_years.flatten(),
        'Publicações': predicted_publications,
        'Tipo': ['Real' if year <= years.max() else 'Previsão' for year in future_years.flatten()]
    })

    # Adicionar dados reais
    df_real = pd.DataFrame({
        'Ano': yearly_production.index,
        'Publicações': yearly_production.values,
        'Tipo': 'Real'
    })

    fig = px.line(
        df_growth,
        x='Ano',
        y='Publicações',
        color='Tipo',
        title=f'Crescimento Exponencial e Previsão (R² = {r2_score:.3f})',
        markers=True
    )

    # Adicionar pontos reais
    fig.add_scatter(
        x=df_real['Ano'],
        y=df_real['Publicações'],
        mode='markers',
        marker=dict(size=10, color='blue'),
        name='Dados Reais'
    )

    st.plotly_chart(fig, use_container_width=True)

    # Informações do modelo
    growth_rate = (np.exp(b) - 1) * 100
    st.info(f"📊 Modelo: y = {a:.2f} * e^({b:.3f}*x)")
    st.info(f"📈 Taxa de crescimento anual: {growth_rate:.2f}%")

    # Previsões
    st.subheader("🔮 Previsões para Próximos Anos")
    predictions_data = []
    for i in range(len(years), len(future_years)):
        year = int(future_years[i][0])
        pred = int(predicted_publications[i])
        predictions_data.append({'Ano': year, 'Previsão': pred})

    df_predictions = pd.DataFrame(predictions_data)
    st.dataframe(df_predictions)

def analyze_international_collaboration(df):
    """Análise de colaboração internacional."""
    st.header("🌍 Colaboração Internacional")

    if 'country_affiliation' not in df.columns:
        st.warning("Dados de afiliação de países não disponíveis")
        return

    # Contadores
    international_collaborations = 0
    single_country_papers = 0
    total_papers_with_countries = 0

    # Análise por país
    country_publications = defaultdict(int)
    country_collaborations = defaultdict(int)
    collaboration_pairs = defaultdict(int)

    for _, row in df.iterrows():
        countries_str = str(row['country_affiliation'])
        if countries_str and countries_str not in ['nan', '']:
            countries = [c.strip() for c in countries_str.split(';') if c.strip()]
            if countries:
                total_papers_with_countries += 1

                # Contar publicações por país
                for country in countries:
                    country_publications[country] += 1

                # Analisar tipo de colaboração
                unique_countries = list(set(countries))
                if len(unique_countries) == 1:
                    single_country_papers += 1
                else:
                    international_collaborations += 1
                    # Contar colaborações entre pares de países
                    for i in range(len(unique_countries)):
                        for j in range(i+1, len(unique_countries)):
                            pair = tuple(sorted([unique_countries[i], unique_countries[j]]))
                            collaboration_pairs[pair] += 1

                    # Contar colaborações por país
                    for country in unique_countries:
                        country_collaborations[country] += 1

    if total_papers_with_countries == 0:
        st.warning("Dados insuficientes para análise de colaboração internacional")
        return

    # Calcular percentuais
    international_rate = (international_collaborations / total_papers_with_countries) * 100
    single_country_rate = (single_country_papers / total_papers_with_countries) * 100

    # Métricas principais
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Colaboração Internacional", f"{international_rate:.1f}%")

    with col2:
        st.metric("Artigos de País Único", f"{single_country_rate:.1f}%")

    with col3:
        st.metric("Artigos com Dados de País", total_papers_with_countries)

    # Top países mais produtivos
    top_countries = sorted(country_publications.items(), key=lambda x: x[1], reverse=True)[:15]

    if top_countries:
        st.subheader("🌍 Top 15 Países por Produção Científica")

        df_countries = pd.DataFrame(top_countries, columns=['País', 'Publicações'])

        fig = px.bar(
            df_countries,
            x='Publicações',
            y='País',
            orientation='h',
            title='Top 15 Países por Produção Científica',
            color='Publicações',
            color_continuous_scale='viridis'
        )

        fig.update_layout(
            yaxis={'categoryorder': 'total ascending'},
            height=600
        )

        st.plotly_chart(fig, use_container_width=True)

        # Mostrar tabela
        st.dataframe(df_countries)

    # Principais colaborações entre países
    if collaboration_pairs:
        st.subheader("🤝 Principais Colaborações entre Países")
        top_collaborations = sorted(collaboration_pairs.items(), key=lambda x: x[1], reverse=True)[:10]

        collab_data = []
        for (country1, country2), count in top_collaborations:
            collab_data.append({
                'Países': f"{country1} - {country2}",
                'Colaborações': count
            })

        df_collaborations = pd.DataFrame(collab_data)
        st.dataframe(df_collaborations)

def analyze_thematic_diversity(df):
    """Análise de diversidade temática (Índice de Shannon)."""
    st.header("🏷️ Diversidade Temática - Índice de Shannon")

    if 'keywords' not in df.columns:
        st.warning("Dados de palavras-chave não disponíveis")
        return

    # Processar palavras-chave
    all_keywords = []
    for keywords_row in df['keywords'].dropna():
        if str(keywords_row).lower() not in ['nan', 'none', '']:
            individual_keywords = re.split(r'[;,]', str(keywords_row))
            clean_keywords = [k.strip().lower().title() for k in individual_keywords
                            if len(k.strip()) > 3 and not k.strip().isdigit()]
            if clean_keywords:
                all_keywords.extend(clean_keywords)

    if not all_keywords:
        st.warning("Dados insuficientes para análise temática")
        return

    # Calcular frequência de palavras-chave
    keyword_counts = Counter(all_keywords)
    total_keywords = len(all_keywords)

    # Calcular Índice de Shannon
    shannon_index = 0
    for count in keyword_counts.values():
        p_i = count / total_keywords
        if p_i > 0:
            shannon_index -= p_i * np.log(p_i)

    # Normalizar o índice de Shannon
    max_shannon = np.log(len(keyword_counts)) if len(keyword_counts) > 1 else 1
    normalized_shannon = shannon_index / max_shannon if max_shannon > 0 else 0

    # Métricas
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Índice de Shannon", f"{shannon_index:.3f}")

    with col2:
        st.metric("Shannon Normalizado", f"{normalized_shannon:.3f}")

    with col3:
        st.metric("Palavras-chave Únicas", len(keyword_counts))

    with col4:
        st.metric("Total de Ocorrências", total_keywords)

    # Interpretação
    if normalized_shannon > 0.8:
        st.success("✅ Alta diversidade temática")
    elif normalized_shannon > 0.6:
        st.info("📊 Diversidade temática moderada")
    else:
        st.warning("⚠️ Baixa diversidade temática")

def analyze_brazil_specific(df):
    """Análise específica da produção científica do Brasil."""
    st.header("🇧🇷 Produção Científica do Brasil")

    if 'country_affiliation' not in df.columns:
        st.warning("Dados de afiliação de países não disponíveis")
        return

    # Filtrar artigos com afiliação do Brasil
    brazil_articles = []
    brazil_collaborations = []

    for _, row in df.iterrows():
        countries_str = str(row['country_affiliation'])
        if countries_str and countries_str not in ['nan', '']:
            countries = [c.strip() for c in countries_str.split(';') if c.strip()]
            # Verificar se Brasil está na lista de países
            if any(country in ['Brazil', 'BR', 'brasil', 'BRAZIL'] for country in countries):
                brazil_articles.append(row)
                # Verificar se é colaboração internacional
                if len(set(countries)) > 1:
                    brazil_collaborations.append(row)

    if not brazil_articles:
        st.warning("⚠️ Não foram encontrados artigos com afiliação brasileira")
        return

    # Converter para DataFrame
    df_brazil = pd.DataFrame(brazil_articles)
    df_brazil_collab = pd.DataFrame(brazil_collaborations)

    # Métricas principais
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Total de Artigos", len(df_brazil))

    with col2:
        collab_rate = (len(df_brazil_collab) / len(df_brazil) * 100) if len(df_brazil) > 0 else 0
        st.metric("Colaborações Internacionais", f"{collab_rate:.1f}%")

    with col3:
        brazil_share = (len(df_brazil) / len(df) * 100) if len(df) > 0 else 0
        st.metric("Participação Global", f"{brazil_share:.1f}%")

    # Análise por ano
    if 'year' in df_brazil.columns:
        st.subheader("📅 Evolução Temporal do Brasil")

        brazil_yearly = df_brazil['year'].value_counts().sort_index()

        # Gráfico de linha da evolução
        fig = px.line(
            x=brazil_yearly.index,
            y=brazil_yearly.values,
            title='Evolução da Produção Científica do Brasil',
            labels={'x': 'Ano', 'y': 'Número de Publicações'},
            markers=True
        )

        fig.update_traces(line=dict(color='green', width=3), marker=dict(size=8))
        fig.update_layout(xaxis_title="Ano", yaxis_title="Número de Publicações")

        st.plotly_chart(fig, use_container_width=True)

        # Tabela por ano
        st.subheader("📊 Dados por Ano")
        yearly_df = pd.DataFrame({
            'Ano': brazil_yearly.index,
            'Publicações': brazil_yearly.values
        })
        st.dataframe(yearly_df)

    # Análise por periódicos
    if 'journal' in df_brazil.columns:
        st.subheader("📰 Principais Periódicos Brasileiros")

        brazil_journals = df_brazil['journal'].value_counts().head(10)

        if len(brazil_journals) > 0:
            fig = px.bar(
                x=brazil_journals.values,
                y=brazil_journals.index,
                orientation='h',
                title='Top 10 Periódicos com Artigos Brasileiros',
                labels={'x': 'Número de Artigos', 'y': 'Periódico'},
                color=brazil_journals.values,
                color_continuous_scale='greens'
            )

            fig.update_layout(
                yaxis={'categoryorder': 'total ascending'},
                height=500
            )

            st.plotly_chart(fig, use_container_width=True)

            # Tabela de periódicos
            journal_df = pd.DataFrame({
                'Periódico': brazil_journals.index,
                'Artigos': brazil_journals.values
            })
            st.dataframe(journal_df)

    # Análise de colaboração por país parceiro
    if len(df_brazil_collab) > 0:
        st.subheader("🤝 Principais Países Colaboradores com o Brasil")

        collaboration_countries = defaultdict(int)
        for _, row in df_brazil_collab.iterrows():
            countries_str = str(row['country_affiliation'])
            if countries_str and countries_str not in ['nan', '']:
                countries = [c.strip() for c in countries_str.split(';') if c.strip()]
                # Contar países parceiros (excluindo Brasil)
                for country in countries:
                    if country not in ['Brazil', 'BR', 'brasil', 'BRAZIL']:
                        collaboration_countries[country] += 1

        if collaboration_countries:
            top_collaborations = sorted(collaboration_countries.items(), key=lambda x: x[1], reverse=True)[:10]

            if top_collaborations:
                # Gráfico de colaborações
                collab_df = pd.DataFrame(top_collaborations, columns=['País', 'Colaborações'])

                fig = px.bar(
                    collab_df,
                    x='Colaborações',
                    y='País',
                    orientation='h',
                    title='Top 10 Países Colaboradores com o Brasil',
                    color='Colaborações',
                    color_continuous_scale='oranges'
                )

                fig.update_layout(
                    yaxis={'categoryorder': 'total ascending'},
                    height=500
                )

                st.plotly_chart(fig, use_container_width=True)

                # Tabela de colaborações
                st.dataframe(collab_df)

    # Análise de autores brasileiros mais produtivos
    st.subheader("👥 Autores Brasileiros Mais Produtivos")

    brazil_authors = []
    for authors_row in df_brazil['authors'].dropna():
        individual_authors = [a.strip() for a in str(authors_row).split(';')
                            if len(a.strip()) > 2]
        brazil_authors.extend(individual_authors)

    if brazil_authors:
        author_counts = Counter(brazil_authors)
        top_brazil_authors = author_counts.most_common(10)

        if top_brazil_authors:
            authors_df = pd.DataFrame(top_brazil_authors, columns=['Autor', 'Publicações'])

            fig = px.bar(
                authors_df,
                x='Publicações',
                y='Autor',
                orientation='h',
                title='Top 10 Autores Brasileiros Mais Produtivos',
                color='Publicações',
                color_continuous_scale='viridis'
            )

            fig.update_layout(
                yaxis={'categoryorder': 'total ascending'},
                height=500
            )

            st.plotly_chart(fig, use_container_width=True)

            # Tabela de autores
            st.dataframe(authors_df)

    # Análise de citações dos artigos brasileiros
    if 'citations' in df_brazil.columns:
        st.subheader("📚 Impacto dos Artigos Brasileiros")

        total_citations_brazil = df_brazil['citations'].sum()
        avg_citations_brazil = df_brazil['citations'].mean()

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Total de Citações", total_citations_brazil)

        with col2:
            st.metric("Média de Citações", f"{avg_citations_brazil:.2f}")

        with col3:
            # Comparar com média global
            global_avg = df['citations'].mean()
            comparison = ((avg_citations_brazil - global_avg) / global_avg * 100) if global_avg > 0 else 0
            st.metric("vs. Média Global", f"{comparison:+.1f}%")

        # Top artigos brasileiros mais citados
        if total_citations_brazil > 0:
            top_cited_brazil = df_brazil.nlargest(5, 'citations')[['title', 'authors', 'year', 'citations', 'journal']]

            st.subheader("🏆 Top 5 Artigos Brasileiros Mais Citados")
            for i, (_, row) in enumerate(top_cited_brazil.iterrows(), 1):
                with st.expander(f"{i}. {row['citations']} citações - {row['title'][:50]}..."):
                    st.write(f"**Autores:** {row['authors']}")
                    st.write(f"**Periódico:** {row['journal']}")
                    st.write(f"**Ano:** {row['year']}")
                    st.write(f"**Citações:** {row['citations']}")

    return df_brazil

def analyze_keyword_cooccurrence(df):
    """Análise de rede de coocorrência das palavras-chave."""
    st.header("🔗 Rede de Coocorrência das Palavras-chave")

    # Parâmetros de configuração
    col1, col2 = st.columns(2)
    with col1:
        min_cooccurrence = st.slider("Coocorrência mínima", 2, 20, 5)
    with col2:
        top_keywords = st.slider("Top palavras-chave", 20, 100, 50)

    try:
        # Extrair e limpar palavras-chave
        all_keywords = []
        keyword_papers = defaultdict(list)

        for idx, keywords_row in df['keywords'].dropna().items():
            if str(keywords_row).lower() not in ['nan', 'none', '']:
                individual_keywords = re.split(r'[;,]', str(keywords_row))
                clean_keywords = [
                    k.strip().lower().title()
                    for k in individual_keywords
                    if len(k.strip()) > 3 and not k.strip().isdigit()
                ]

                # Armazenar quais papers contêm cada palavra-chave
                for keyword in clean_keywords:
                    keyword_papers[keyword].append(idx)

                all_keywords.extend(clean_keywords)

        # Contar frequência das palavras-chave
        keyword_counts = Counter(all_keywords)
        top_keywords_list = [kw for kw, count in keyword_counts.most_common(top_keywords)]

        # Calcular matriz de coocorrência
        cooccurrence_matrix = defaultdict(lambda: defaultdict(int))

        for keyword1 in top_keywords_list:
            papers1 = set(keyword_papers[keyword1])
            for keyword2 in top_keywords_list:
                if keyword1 != keyword2:
                    papers2 = set(keyword_papers[keyword2])
                    cooccurrence = len(papers1.intersection(papers2))
                    if cooccurrence >= min_cooccurrence:
                        cooccurrence_matrix[keyword1][keyword2] = cooccurrence

        # Criar rede de coocorrência
        if cooccurrence_matrix:
            # Preparar dados para o grafo
            edges = []
            nodes = set()

            for kw1, connections in cooccurrence_matrix.items():
                for kw2, weight in connections.items():
                    edges.append((kw1, kw2, weight))
                    nodes.add(kw1)
                    nodes.add(kw2)

            if edges:
                # Criar grafo usando NetworkX
                import networkx as nx

                G = nx.Graph()
                for kw1, kw2, weight in edges:
                    G.add_edge(kw1, kw2, weight=weight)

                # Layout do grafo
                pos = nx.spring_layout(G, k=3, iterations=50)

                # Preparar dados para Plotly
                edge_x = []
                edge_y = []
                edge_info = []

                for edge in G.edges():
                    x0, y0 = pos[edge[0]]
                    x1, y1 = pos[edge[1]]
                    edge_x.extend([x0, x1, None])
                    edge_y.extend([y0, y1, None])
                    weight = G[edge[0]][edge[1]]['weight']
                    edge_info.append(f"{edge[0]} ↔ {edge[1]}: {weight} coocorrências")

                # Nós
                node_x = []
                node_y = []
                node_text = []
                node_size = []

                for node in G.nodes():
                    x, y = pos[node]
                    node_x.append(x)
                    node_y.append(y)
                    node_text.append(node)
                    # Tamanho baseado na frequência da palavra-chave
                    size = keyword_counts[node]
                    node_size.append(min(50, max(10, size)))

                # Criar gráfico
                fig = go.Figure()

                # Adicionar arestas
                fig.add_trace(go.Scatter(
                    x=edge_x, y=edge_y,
                    line=dict(width=1, color='lightgray'),
                    hoverinfo='none',
                    mode='lines',
                    showlegend=False
                ))

                # Adicionar nós
                fig.add_trace(go.Scatter(
                    x=node_x, y=node_y,
                    mode='markers+text',
                    hoverinfo='text',
                    text=node_text,
                    textposition="middle center",
                    textfont=dict(size=8),
                    marker=dict(
                        size=node_size,
                        color='lightblue',
                        line=dict(width=2, color='darkblue')
                    ),
                    hovertext=[f"{node}<br>Frequência: {keyword_counts[node]}" for node in G.nodes()],
                    showlegend=False
                ))

                fig.update_layout(
                    title=f'Rede de Coocorrência das Palavras-chave (min. {min_cooccurrence} coocorrências)',
                    showlegend=False,
                    hovermode='closest',
                    margin=dict(b=20,l=5,r=5,t=40),
                    annotations=[ dict(
                        text="Tamanho dos nós = frequência da palavra-chave",
                        showarrow=False,
                        xref="paper", yref="paper",
                        x=0.005, y=-0.002,
                        xanchor='left', yanchor='bottom',
                        font=dict(size=12)
                    )],
                    xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                    yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                    height=600
                )

                st.plotly_chart(fig, use_container_width=True)

                # Estatísticas da rede
                st.subheader("📊 Estatísticas da Rede")
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("Nós (Palavras-chave)", len(G.nodes()))

                with col2:
                    st.metric("Arestas (Coocorrências)", len(G.edges()))

                with col3:
                    density = nx.density(G)
                    st.metric("Densidade", f"{density:.3f}")

                with col4:
                    if len(G.nodes()) > 0:
                        avg_degree = sum(dict(G.degree()).values()) / len(G.nodes())
                        st.metric("Grau Médio", f"{avg_degree:.1f}")

                # Top coocorrências
                st.subheader("🔝 Principais Coocorrências")
                top_cooccurrences = sorted(edges, key=lambda x: x[2], reverse=True)[:10]

                cooc_df = pd.DataFrame(top_cooccurrences, columns=['Palavra-chave 1', 'Palavra-chave 2', 'Coocorrências'])
                st.dataframe(cooc_df)

            else:
                st.warning(f"Nenhuma coocorrência encontrada com o mínimo de {min_cooccurrence}")
        else:
            st.warning("Não foi possível gerar a rede de coocorrência")

    except Exception as e:
        st.error(f"Erro na análise de coocorrência: {e}")
        st.info("Certifique-se de que o NetworkX está instalado: pip install networkx")

def analyze_institution_collaboration(df):
    """Análise de rede de colaboração entre instituições."""
    st.header("🏛️ Rede de Colaboração entre Instituições")

    if 'institutions' not in df.columns:
        st.warning("Dados de instituições não disponíveis no dataset")
        return

    # Parâmetros de configuração
    col1, col2 = st.columns(2)
    with col1:
        min_collaboration = st.slider("Colaborações mínimas", 1, 10, 2)
    with col2:
        top_institutions = st.slider("Top instituições", 20, 100, 50)

    try:
        # Extrair e limpar instituições
        all_institutions = []
        institution_papers = defaultdict(list)

        for idx, institutions_row in df['institutions'].dropna().items():
            if str(institutions_row).lower() not in ['nan', 'none', '']:
                individual_institutions = re.split(r'[;,]', str(institutions_row))
                clean_institutions = [
                    inst.strip()
                    for inst in individual_institutions
                    if len(inst.strip()) > 5  # Filtrar nomes muito curtos
                ]

                # Armazenar quais papers contêm cada instituição
                for institution in clean_institutions:
                    institution_papers[institution].append(idx)

                all_institutions.extend(clean_institutions)

        if not all_institutions:
            st.warning("Nenhuma instituição encontrada nos dados")
            return

        # Contar frequência das instituições
        institution_counts = Counter(all_institutions)
        top_institutions_list = [inst for inst, count in institution_counts.most_common(top_institutions)]

        # Calcular matriz de colaboração
        collaboration_matrix = defaultdict(lambda: defaultdict(int))

        for inst1 in top_institutions_list:
            papers1 = set(institution_papers[inst1])
            for inst2 in top_institutions_list:
                if inst1 != inst2:
                    papers2 = set(institution_papers[inst2])
                    collaboration = len(papers1.intersection(papers2))
                    if collaboration >= min_collaboration:
                        collaboration_matrix[inst1][inst2] = collaboration

        # Criar rede de colaboração
        if collaboration_matrix:
            # Preparar dados para o grafo
            edges = []
            nodes = set()

            for inst1, connections in collaboration_matrix.items():
                for inst2, weight in connections.items():
                    edges.append((inst1, inst2, weight))
                    nodes.add(inst1)
                    nodes.add(inst2)

            if edges:
                # Criar grafo usando NetworkX
                import networkx as nx

                G = nx.Graph()
                for inst1, inst2, weight in edges:
                    G.add_edge(inst1, inst2, weight=weight)

                # Layout do grafo
                pos = nx.spring_layout(G, k=5, iterations=50)

                # Preparar dados para Plotly
                edge_x = []
                edge_y = []
                edge_weights = []

                for edge in G.edges():
                    x0, y0 = pos[edge[0]]
                    x1, y1 = pos[edge[1]]
                    edge_x.extend([x0, x1, None])
                    edge_y.extend([y0, y1, None])
                    weight = G[edge[0]][edge[1]]['weight']
                    edge_weights.append(weight)

                # Nós
                node_x = []
                node_y = []
                node_text = []
                node_size = []
                node_hover = []

                for node in G.nodes():
                    x, y = pos[node]
                    node_x.append(x)
                    node_y.append(y)
                    # Truncar nome da instituição para exibição
                    display_name = node[:30] + "..." if len(node) > 30 else node
                    node_text.append(display_name)
                    node_hover.append(f"{node}<br>Publicações: {institution_counts[node]}<br>Colaborações: {G.degree[node]}")
                    # Tamanho baseado no número de publicações
                    size = institution_counts[node]
                    node_size.append(min(60, max(15, size * 3)))

                # Criar gráfico
                fig = go.Figure()

                # Adicionar arestas com espessura baseada no peso
                max_weight = max(edge_weights) if edge_weights else 1
                for i in range(0, len(edge_x), 3):
                    if i//3 < len(edge_weights):
                        weight = edge_weights[i//3]
                        line_width = max(1, (weight / max_weight) * 5)
                        fig.add_trace(go.Scatter(
                            x=edge_x[i:i+2], y=edge_y[i:i+2],
                            line=dict(width=line_width, color='lightgray'),
                            hoverinfo='none',
                            mode='lines',
                            showlegend=False
                        ))

                # Adicionar nós
                fig.add_trace(go.Scatter(
                    x=node_x, y=node_y,
                    mode='markers+text',
                    hoverinfo='text',
                    text=node_text,
                    textposition="middle center",
                    textfont=dict(size=6),
                    marker=dict(
                        size=node_size,
                        color='lightcoral',
                        line=dict(width=2, color='darkred')
                    ),
                    hovertext=node_hover,
                    showlegend=False
                ))

                fig.update_layout(
                    title=f'Rede de Colaboração entre Instituições (min. {min_collaboration} colaborações)',
                    showlegend=False,
                    hovermode='closest',
                    margin=dict(b=20,l=5,r=5,t=40),
                    annotations=[ dict(
                        text="Tamanho dos nós = número de publicações | Espessura das linhas = força da colaboração",
                        showarrow=False,
                        xref="paper", yref="paper",
                        x=0.005, y=-0.002,
                        xanchor='left', yanchor='bottom',
                        font=dict(size=10)
                    )],
                    xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                    yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                    height=700
                )

                st.plotly_chart(fig, use_container_width=True)

                # Estatísticas da rede
                st.subheader("📊 Estatísticas da Rede")
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("Nós (Instituições)", len(G.nodes()))

                with col2:
                    st.metric("Arestas (Colaborações)", len(G.edges()))

                with col3:
                    density = nx.density(G)
                    st.metric("Densidade", f"{density:.3f}")

                with col4:
                    if len(G.nodes()) > 0:
                        avg_degree = sum(dict(G.degree()).values()) / len(G.nodes())
                        st.metric("Grau Médio", f"{avg_degree:.1f}")

                # Top colaborações
                st.subheader("🤝 Principais Colaborações")
                top_collaborations = sorted(edges, key=lambda x: x[2], reverse=True)[:10]

                collab_df = pd.DataFrame(top_collaborations, columns=['Instituição 1', 'Instituição 2', 'Colaborações'])
                st.dataframe(collab_df)

                # Instituições mais centrais
                st.subheader("🏆 Instituições Mais Centrais")
                centrality = nx.degree_centrality(G)
                top_central = sorted(centrality.items(), key=lambda x: x[1], reverse=True)[:10]

                central_df = pd.DataFrame(top_central, columns=['Instituição', 'Centralidade'])
                central_df['Centralidade'] = central_df['Centralidade'].round(3)
                st.dataframe(central_df)

            else:
                st.warning(f"Nenhuma colaboração encontrada com o mínimo de {min_collaboration}")
        else:
            st.warning("Não foi possível gerar a rede de colaboração")

    except Exception as e:
        st.error(f"Erro na análise de colaboração institucional: {e}")
        st.info("Certifique-se de que o NetworkX está instalado: pip install networkx")

def main():
    """Função principal do app Streamlit."""
    st.title("📊 Análise Bibliométrica: Processamento de Linguagem Natural e LLMs Aplicados a Prontuários Eletrônicos")
    st.markdown("Análise bibliométrica de artigos sobre Large Language Models (LLMs) em Electronic Health Records (EHR) - V. F. Lima")
    st.markdown("Disciplina de Estudos Métricos da Pós-Graduação em Ciência da Informação da Unesp Campus de Marília.")
    st.markdown("---")

    # Sidebar para navegação
    st.sidebar.title("🔍 Navegação")
    st.sidebar.markdown("Selecione as análises que deseja visualizar:")

    # Carregar dados
    df = load_data()

    if df.empty:
        st.error("Não foi possível carregar os dados. Verifique se o arquivo 'dataset_llm_ehr_curado.csv' está no diretório correto.")
        return

    # Opções de análise
    analyses = {
        "📊 Visão Geral": display_dataset_overview,
        "📈 Evolução Temporal": analyze_temporal_evolution,
        "👥 Autores Produtivos": analyze_top_authors,
        "📰 Periódicos": analyze_journals,
        "🔤 Palavras-Chave": analyze_keywords,
        "🕸️ Rede de Coautoria": analyze_collaboration_network,
        "🔗 Rede de Coocorrência": analyze_keyword_cooccurrence,
        "🏛️ Rede de Instituições": analyze_institution_collaboration,
        "📚 Análise de Citações": analyze_citations,
        "📊 Lei de Lotka": analyze_lotka_law,
        "📈 Crescimento Exponencial": analyze_exponential_growth,
        "🌍 Colaboração Internacional": analyze_international_collaboration,
        "🏷️ Diversidade Temática": analyze_thematic_diversity,
        "🇧🇷 Produção do Brasil": analyze_brazil_specific
    }

    # Checkboxes para seleção de análises
    selected_analyses = []
    for analysis_name in analyses.keys():
        if st.sidebar.checkbox(analysis_name, value=(analysis_name == "📊 Visão Geral")):
            selected_analyses.append(analysis_name)

    # Executar análises selecionadas
    if selected_analyses:
        for analysis_name in selected_analyses:
            try:
                analyses[analysis_name](df)
                st.markdown("---")
            except Exception as e:
                st.error(f"Erro na análise '{analysis_name}': {e}")
    else:
        st.info("Selecione pelo menos uma análise na barra lateral.")

    # Informações adicionais
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 📋 Sobre")
    st.sidebar.markdown("""
    Este app realiza análise bibliométrica de artigos sobre
    Large Language Models (LLMs) em Electronic Health Records (EHR).

    **Período:** 2020-2024

    **Análises disponíveis:**
    - Evolução temporal
    - Produtividade de autores
    - Análise de periódicos
    - Palavras-chave e temas
    - Redes de colaboração
    - Métricas bibliométricas
    - Análise específica do Brasil
    """)

if __name__ == "__main__":
    main()
