# -*- coding: utf-8 -*-
"""bibliometric_analysis_enhanced.ipynb

Automatically generated by Colab.

Original file is located at
    https://colab.research.google.com/drive/1C4ZSqDktQN-odpoEgpoMp9TsOFj-DBZW
"""

# -*- coding: utf-8 -*-
"""
Análise Bibliométrica: LLMs em Sistemas de Recuperação de Informação em Saúde
Trabalho desenvolvido conforme diretrizes da disciplina de Estudos Métricos
Período de análise: 2020-2024 (era dos LLMs)
"""

# ========================================
# INSTALAÇÃO E IMPORTAÇÃO DE BIBLIOTECAS
# ========================================

!pip install biopython pandas matplotlib seaborn plotly wordcloud networkx unidecode requests scikit-learn python-louvain -q



#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Análise Bibliométrica: LLMs em Sistemas de Recuperação de Informação em Saúde
Versão refatorada com melhorias em estrutura, performance e manutenibilidade.

Autor: Sistema de Análise Bibliométrica
Data: 2024
Período de análise: 2020-2024 (era dos LLMs)
"""

import json
import re
import time
import warnings
from collections import Counter, defaultdict
from datetime import datetime
from itertools import combinations
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
from urllib.parse import urlencode

import matplotlib.pyplot as plt
import networkx as nx
import numpy as np
import pandas as pd
import requests
import seaborn as sns
from Bio import Entrez, Medline
from sklearn.cluster import KMeans
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from wordcloud import WordCloud

warnings.filterwarnings("ignore")

# Configurações globais
ENTREZ_EMAIL = "<EMAIL>"
OUTPUT_DIR = Path("outputs")
OUTPUT_DIR.mkdir(exist_ok=True)

# Configuração de estilo para gráficos
sns.set_style("whitegrid")
plt.rcParams.update({
    'figure.figsize': (12, 8),
    'font.size': 11,
    'axes.titlesize': 14,
    'axes.titleweight': 'bold'
})

# Queries de busca refinadas
PUBMED_QUERY = '''
(
  ("Natural Language Processing*"[Title/Abstract/Keywords] OR "NLP*"[Title/Abstract/Keywords] OR "Large Language Model*"[Title/Abstract/Keywords/Keywords] OR "Large Language Models*"[Title/Abstract/Keywords] OR "LLM*"[Title/Abstract/Keywords] OR "llms*"[Title/Abstract/Keywords] OR "GPT*"[Title/Abstract/Keywords] OR "BERT*"[Title/Abstract/Keywords] OR "transformer*"[Title/Abstract/Keywords] OR "generative AI*"[Title/Abstract/Keywords] OR "ChatGPT"[Title/Abstract/Keywords] OR "LLaMA*"[Title/Abstract/Keywords] OR "Llama*"[Title/Abstract/Keywords] OR "Claude*"[Title/Abstract/Keywords] OR "Gemini*"[Title/Abstract/Keywords] OR "Google Gemini"[Title/Abstract/Keywords] OR "RoBERTa*"[Title/Abstract/Keywords] OR "Qwen*"[Title/Abstract/Keywords] OR "Qwen2*"[Title/Abstract/Keywords] OR "Qwen3*"[Title/Abstract/Keywords] OR "DeepSeek*"[Title/Abstract/Keywords])
  AND
  ("electronic health record*"[Title/Abstract/Keywords] OR "EHR*"[Title/Abstract/Keywords] OR
   "electronic medical record*"[Title/Abstract/Keywords] OR "EMR*"[Title/Abstract/Keywords] OR
   "clinical record*"[Title/Abstract/Keywords] OR "patient record*"[Title/Abstract/Keywords] OR
   "medical record*"[Title/Abstract/Keywords] OR "health information system*"[Title/Abstract/Keywords])
  AND
  ("information retrieval*"[Title/Abstract/Keywords] OR "information extraction*"[Title/Abstract/Keywords] OR
   "text mining*"[Title/Abstract/Keywords] OR
   "semantic search*"[Title/Abstract/Keywords] OR "clinical decision support*"[Title/Abstract/Keywords])
  AND
  ("2020"[Date - Publication] : "2024"[Date - Publication])
)
'''

OPENALEX_QUERY = '''
(
  ("natural language processing" OR "NLP" OR "large language model" OR "large language models" OR "LLM" OR "llms" OR "Natural Language Processing" OR "NLP" OR "Large Language Model" OR "Large Language Models" OR "LLMs" OR "llms" OR "GPT" OR "BERT" OR "transformer" OR "generative AI" OR "ChatGPT" OR "LLaMA" OR "Llama" OR "Claude" OR "Gemini" OR "Google Gemini" OR "RoBERTa" OR "Qwen" OR "Qwen2" OR "Qwen3" OR "DeepSeek")
  AND
  ("electronic health record" OR "EHR" OR "electronic medical record" OR "EMR" OR "clinical record" OR "patient record" OR "medical record" OR "health information system")
  AND
  ("information retrieval" OR "information extraction" OR "text mining" OR "semantic search" OR "clinical decision support")
)
'''


class BibliometricAnalyzer:
    """Classe principal para análise bibliométrica."""

    def __init__(self, email: str = ENTREZ_EMAIL, output_dir: str = "outputs"):
        self.email = email
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        Entrez.email = email
        self.df = pd.DataFrame()

    def collect_data(self, max_results: Optional[int] = None) -> pd.DataFrame:
        """Coleta dados de múltiplas fontes."""
        print("🚀 INICIANDO COLETA DE DADOS")
        print("=" * 50)

        datasets = []

        # Coleta PubMed
        try:
            df_pubmed = self._search_pubmed(PUBMED_QUERY, max_results)
            if not df_pubmed.empty:
                datasets.append(df_pubmed)
                print(f"📚 PubMed: {len(df_pubmed)} artigos coletados")
        except Exception as e:
            print(f"❌ Erro no PubMed: {e}")

        # Coleta OpenAlex
        try:
            df_openalex = self._search_openalex(OPENALEX_QUERY, max_results)
            if not df_openalex.empty:
                datasets.append(df_openalex)
                print(f"🌐 OpenAlex: {len(df_openalex)} artigos coletados")
        except Exception as e:
            print(f"❌ Erro no OpenAlex: {e}")

        if not datasets:
            raise ValueError("Nenhum artigo foi coletado das fontes disponíveis")

        self.df = pd.concat(datasets, ignore_index=True, sort=False)
        print(f"📊 Total coletado: {len(self.df)} artigos")

        return self.df

    def _search_pubmed(self, query: str, max_results: Optional[int]) -> pd.DataFrame:
        """Busca artigos no PubMed."""
        print("🔍 Buscando no PubMed...")

        retmax = max_results or 10000
        handle = Entrez.esearch(db="pubmed", term=query, retmax=retmax, usehistory="y")
        record = Entrez.read(handle)
        handle.close()

        id_list = record["IdList"]
        if not id_list:
            return pd.DataFrame()

        records_data = []
        batch_size = 100

        for i in range(0, len(id_list), batch_size):
            batch_ids = id_list[i:i+batch_size]
            print(f"   📥 Processando lote {i//batch_size + 1}/{len(id_list)//batch_size + 1}")

            handle = Entrez.efetch(db="pubmed", id=batch_ids, rettype="medline", retmode="text")
            batch_records = Medline.parse(handle)

            for rec in batch_records:
                record_data = self._process_pubmed_record(rec)
                if record_data:
                    records_data.append(record_data)

            handle.close()
            time.sleep(0.3)  # Rate limiting

        return pd.DataFrame(records_data)

    def _process_pubmed_record(self, record: Dict) -> Optional[Dict]:
        """Processa um registro individual do PubMed."""
        title = record.get("TI", "").strip()
        if len(title) < 10:
            return None

        # Processar autores
        authors_raw = record.get("AU", [])
        authors = "; ".join(authors_raw) if isinstance(authors_raw, list) else str(authors_raw)

        # Processar palavras-chave
        keywords = []
        for key in ["OT", "MH"]:  # Other Terms, MeSH Terms
            terms = record.get(key, [])
            if isinstance(terms, list):
                keywords.extend(terms)
            elif terms:
                keywords.append(str(terms))

        # Extrair ano
        date_pub = record.get("DP", "")
        year_match = re.search(r"(20\d{2})", str(date_pub))
        year = int(year_match.group(1)) if year_match else None

        # Processar DOI
        doi_raw = record.get("LID", "") or record.get("AID", "")
        doi = self._extract_doi(doi_raw)

        # Link do artigo
        pmid = record.get("PMID", "")
        article_link = f"https://pubmed.ncbi.nlm.nih.gov/{pmid}/" if pmid else ""

        return {
            "source": "PubMed",
            "pmid": pmid,
            "title": title,
            "authors": authors.strip(),
            "journal": record.get("SO", "").strip(),
            "year": year,
            "abstract": record.get("AB", "").strip(),
            "doi": doi,
            "article_link": article_link,
            "keywords": "; ".join(keywords),
            "citations": 0,
            "country_affiliation": "",
            "study_type": record.get("PT", "")
        }

    def _search_openalex(self, query: str, max_results: Optional[int]) -> pd.DataFrame:
        """Busca artigos no OpenAlex."""
        print("🌐 Buscando no OpenAlex...")

        base_url = "https://api.openalex.org/works"
        params = {
            "search": query,
            "filter": "language:en,from_publication_date:2020-01-01,to_publication_date:2024-12-31,type:article",
            "per-page": 200,
            "cursor": "*",
            "select": "id,title,authorships,primary_location,publication_year,abstract_inverted_index,doi,keywords,cited_by_count,concepts"
        }

        headers = {"User-Agent": "BibliometricStudy/2.0 (mailto:<EMAIL>)"}

        all_records = []
        page = 1
        max_pages = (max_results // 200) + 1 if max_results else 25

        while page <= max_pages:
            try:
                print(f"   📄 Processando página {page}/{max_pages}")

                response = requests.get(f"{base_url}?{urlencode(params)}",
                                     headers=headers, timeout=30)
                response.raise_for_status()

                data = response.json()
                results = data.get("results", [])

                if not results:
                    break

                for item in results:
                    record_data = self._process_openalex_record(item)
                    if record_data:
                        all_records.append(record_data)

                # Próxima página
                next_cursor = data.get("meta", {}).get("next_cursor")
                if not next_cursor:
                    break

                params["cursor"] = next_cursor
                page += 1
                time.sleep(0.5)  # Rate limiting

            except Exception as e:
                print(f"   ⚠️ Erro na página {page}: {e}")
                break

        return pd.DataFrame(all_records)

    def _process_openalex_record(self, item: Dict) -> Optional[Dict]:
        """Processa um registro individual do OpenAlex."""
        title = (item.get("title") or "").strip()
        if len(title) < 10:
            return None

        # Processar autores e afiliações
        authorships = item.get("authorships", [])
        authors = []
        countries = []
        institutions = []

        for authorship in authorships:
            author = authorship.get("author", {})
            if author.get("display_name"):
                authors.append(author["display_name"])

            for institution in authorship.get("institutions", []):
                if institution.get("display_name"):
                    institutions.append(institution["display_name"])
                if institution.get("country_code"):
                    countries.append(institution["country_code"])

        # Processar abstract
        abstract = self._reconstruct_abstract(item.get("abstract_inverted_index", {}))

        # Processar conceitos e palavras-chave
        concepts = [c.get("display_name", "") for c in item.get("concepts", [])[:10]]
        keywords = [k.get("keyword", "") for k in item.get("keywords", [])]
        all_keywords = concepts + keywords

        # Informações da revista
        primary_location = item.get("primary_location", {}) or {}
        source = primary_location.get("source", {}) or {}
        journal = source.get("display_name", "Unknown Journal")

        # Link do artigo
        doi = (item.get("doi") or "").replace("https://doi.org/", "")
        article_link = f"https://doi.org/{doi}" if doi else item.get("id", "")

        return {
            "source": "OpenAlex",
            "openalex_id": item.get("id", ""),
            "title": title,
            "authors": "; ".join(authors),
            "journal": journal,
            "year": item.get("publication_year"),
            "abstract": abstract[:2000],  # Limitar tamanho
            "doi": doi,
            "article_link": article_link,
            "keywords": "; ".join(filter(None, all_keywords)),
            "citations": item.get("cited_by_count", 0),
            "country_affiliation": "; ".join(list(set(countries))),
            "institutions": "; ".join(list(set(institutions))),
            "study_type": "Article"
        }

    def _extract_doi(self, doi_raw: Union[str, List]) -> str:
        """Extrai DOI limpo de diferentes formatos."""
        if not doi_raw:
            return ""

        if isinstance(doi_raw, list):
            for item in doi_raw:
                if "doi" in str(item).lower():
                    return str(item).replace(" [doi]", "").replace("doi: ", "")
            return ""

        return str(doi_raw).replace(" [doi]", "").replace("doi: ", "")

    def _reconstruct_abstract(self, abstract_dict: Dict) -> str:
        """Reconstrói abstract do formato invertido do OpenAlex."""
        if not abstract_dict:
            return ""

        word_positions = []
        for word, positions in abstract_dict.items():
            for pos in positions:
                word_positions.append((pos, word))

        word_positions.sort()
        return " ".join([word for _, word in word_positions])

    def curate_data(self) -> pd.DataFrame:
        """Realiza curadoria completa dos dados."""
        print("🔧 INICIANDO CURADORIA DOS DADOS")
        print("=" * 50)

        initial_size = len(self.df)

        # 1. Remover duplicatas
        print("1️⃣ Removendo duplicatas...")
        self.df['title_clean'] = self.df['title'].str.lower().str.strip()
        self.df.drop_duplicates(subset=['title_clean'], keep='first', inplace=True)
        print(f"   Removidas {initial_size - len(self.df)} duplicatas")

        # 2. Filtros de qualidade
        print("2️⃣ Aplicando filtros de qualidade...")
        self.df = self.df[
            (self.df['title'].str.len() > 10) &
            (self.df['year'].between(2020, 2024)) &
            (self.df['authors'].str.len() > 3)
        ].copy()

        # 3. Remover preprints e reviews
        exclude_types = ['preprint', 'review']
        mask = ~self.df['study_type'].str.contains('|'.join(exclude_types), case=False, na=False)
        self.df = self.df[mask].copy()

        # 4. Verificar relevância
        print("3️⃣ Verificando relevância...")
        relevant_terms = [
            'large language model', 'llm', 'large language models', 'llms', 'gpt', 'bert', 'transformer',
            'health record', 'ehr', 'emr', 'clinical', 'medical record',
            'information retrieval', 'text mining', 'nlp', 'natural language processing'
        ]

        def is_relevant(text: str) -> bool:
            if pd.isna(text):
                return False
            text_lower = str(text).lower()
            return any(term in text_lower for term in relevant_terms)

        self.df['relevant'] = (
            self.df['title'].apply(is_relevant) |
            self.df['abstract'].apply(is_relevant)
        )
        self.df = self.df[self.df['relevant']].copy()

        # 5. Limpeza final
        self.df.drop(['title_clean', 'relevant'], axis=1, inplace=True)
        self.df.reset_index(drop=True, inplace=True)

        # Padronizar dados
        self.df['journal'] = self.df['journal'].str.strip().str.title()
        self.df['authors'] = self.df['authors'].str.replace(r'\s+', ' ', regex=True)

        reduction_pct = ((initial_size - len(self.df)) / initial_size) * 100
        print(f"✅ Curadoria concluída: {len(self.df)} artigos finais (redução: {reduction_pct:.1f}%)")

        return self.df

    def analyze_temporal_evolution(self) -> None:
        """Análise da evolução temporal."""
        print("📊 Análise temporal...")

        yearly_production = self.df['year'].value_counts().sort_index()

        plt.figure(figsize=(12, 6))
        sns.barplot(x=yearly_production.index, y=yearly_production.values, palette='viridis')
        plt.title('Evolução da Produção Científica sobre LLMs em EHR (2020-2024)')
        plt.xlabel('Ano de Publicação')
        plt.ylabel('Número de Publicações')

        # Adicionar valores nas barras
        for i, v in enumerate(yearly_production.values):
            plt.text(i, v + 0.5, str(v), ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        plt.savefig(self.output_dir / 'temporal_evolution.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Calcular taxa de crescimento
        if len(yearly_production) > 1:
            growth_rate = ((yearly_production.iloc[-1] - yearly_production.iloc[0]) /
                          yearly_production.iloc[0] * 100)
            print(f"   📈 Taxa de crescimento 2020-2024: {growth_rate:.1f}%")

    def analyze_top_authors(self, top_n: int = 15) -> None:
        """Análise dos autores mais produtivos."""
        print("👥 Analisando autores...")

        all_authors = []
        for authors_row in self.df['authors'].dropna():
            individual_authors = [a.strip() for a in str(authors_row).split(';')
                                if len(a.strip()) > 2]
            all_authors.extend(individual_authors)

        author_counts = Counter(all_authors)
        top_authors = author_counts.most_common(top_n)

        df_authors = pd.DataFrame(top_authors, columns=['Autor', 'Publicações'])

        plt.figure(figsize=(12, 8))
        sns.barplot(data=df_authors, y='Autor', x='Publicações', palette='coolwarm')
        plt.title(f'Top {top_n} Autores Mais Produtivos')
        plt.xlabel('Número de Publicações')
        plt.tight_layout()
        plt.savefig(self.output_dir / 'top_authors.png', dpi=300, bbox_inches='tight')
        plt.show()

        return df_authors

    def analyze_journals(self, top_n: int = 10) -> None:
        """Análise dos periódicos mais relevantes."""
        print("📰 Analisando periódicos...")

        journal_counts = self.df['journal'].value_counts().head(top_n)

        plt.figure(figsize=(12, 8))
        sns.barplot(y=journal_counts.index, x=journal_counts.values, palette='muted')
        plt.title(f'Top {top_n} Periódicos com Maior Concentração de Artigos')
        plt.xlabel('Número de Artigos')
        plt.ylabel('Periódico')
        plt.tight_layout()
        plt.savefig(self.output_dir / 'top_journals.png', dpi=300, bbox_inches='tight')
        plt.show()

        return journal_counts

    def analyze_keywords(self, top_n: int = 25) -> None:
        """Análise de palavras-chave."""
        print("🔤 Analisando palavras-chave...")

        all_keywords = []
        for keywords_row in self.df['keywords'].dropna():
            if str(keywords_row).lower() not in ['nan', 'none', '']:
                individual_keywords = re.split(r'[;,]', str(keywords_row))
                clean_keywords = [
                    k.strip().lower().title()
                    for k in individual_keywords
                    if len(k.strip()) > 3 and not k.strip().isdigit()
                ]
                all_keywords.extend(clean_keywords)

        keyword_counts = Counter(all_keywords)
        top_keywords = keyword_counts.most_common(top_n)

        if top_keywords:
            df_keywords = pd.DataFrame(top_keywords, columns=['Palavra-chave', 'Frequência'])

            # Gráfico de barras
            plt.figure(figsize=(12, 10))
            sns.barplot(data=df_keywords.head(15), y='Palavra-chave', x='Frequência', palette='viridis')
            plt.title('Top 15 Palavras-Chave Mais Frequentes')
            plt.tight_layout()
            plt.savefig(self.output_dir / 'top_keywords.png', dpi=300, bbox_inches='tight')
            plt.show()

            # Nuvem de palavras
            if len(all_keywords) > 10:
                self._generate_wordcloud(all_keywords)

        return top_keywords

    def _generate_wordcloud(self, keywords: List[str]) -> None:
        """Gera nuvem de palavras."""
        plt.figure(figsize=(15, 8))
        wordcloud = WordCloud(
            width=800, height=400,
            background_color='white',
            colormap='viridis',
            max_words=100,
            relative_scaling=0.5
        ).generate(' '.join(keywords))

        plt.imshow(wordcloud, interpolation='bilinear')
        plt.axis('off')
        plt.title('Nuvem de Palavras-Chave', fontsize=16, fontweight='bold', pad=20)
        plt.tight_layout()
        plt.savefig(self.output_dir / 'wordcloud.png', dpi=300, bbox_inches='tight')
        plt.show()

    def analyze_collaboration_network(self) -> Optional[nx.Graph]:
        """Análise de rede de coautoria."""
        print("🕸️ Analisando rede de coautoria...")

        edges = []
        for authors_row in self.df['authors'].dropna():
            authors_list = [a.strip() for a in str(authors_row).split(';')
                           if len(a.strip()) > 2]
            if len(authors_list) > 1:
                edges.extend(combinations(authors_list, 2))

        if not edges:
            print("   ⚠️ Dados insuficientes para rede de coautoria")
            return None

        G = nx.Graph()
        G.add_edges_from(edges)

        # Filtrar por grau mínimo
        degree_dict = dict(G.degree())
        relevant_nodes = [node for node, degree in degree_dict.items() if degree >= 2]
        G_filtered = G.subgraph(relevant_nodes[:30])

        if len(G_filtered.nodes) > 3:
            plt.figure(figsize=(16, 12))
            pos = nx.spring_layout(G_filtered, k=1, iterations=50)

            node_sizes = [degree_dict[node] * 300 for node in G_filtered.nodes]

            nx.draw_networkx_nodes(G_filtered, pos, node_size=node_sizes,
                                 node_color='lightcoral', alpha=0.7)
            nx.draw_networkx_edges(G_filtered, pos, edge_color='gray',
                                 alpha=0.5, width=1)
            nx.draw_networkx_labels(G_filtered, pos, font_size=10, font_weight='bold')

            plt.title('Rede de Coautoria (Autores com 2+ Colaborações)')
            plt.axis('off')
            plt.tight_layout()
            plt.savefig(self.output_dir / 'collaboration_network.png', dpi=300, bbox_inches='tight')
            plt.show()

            # Métricas da rede
            print(f"   📊 Métricas da rede:")
            print(f"      Autores: {G.number_of_nodes()}")
            print(f"      Colaborações: {G.number_of_edges()}")
            print(f"      Densidade: {nx.density(G):.3f}")

        return G

    def analyze_citations(self) -> None:
        """Análise de citações."""
        print("📚 Analisando citações...")

        if 'citations' not in self.df.columns or self.df['citations'].sum() == 0:
            print("   ⚠️ Dados de citação não disponíveis")
            return

        # Top artigos citados
        top_cited = self.df.nlargest(10, 'citations')[['title', 'authors', 'year', 'citations', 'journal']]

        print("📚 Top 10 artigos mais citados:")
        for _, row in top_cited.iterrows():
            print(f"   {row['citations']:>3} citações: {row['title'][:60]}... ({row['year']})")

        # Distribuição de citações
        df_with_citations = self.df[self.df['citations'] > 0]
        if len(df_with_citations) > 0:
            plt.figure(figsize=(12, 6))
            plt.hist(df_with_citations['citations'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            plt.axvline(df_with_citations['citations'].mean(), color='red', linestyle='--',
                       label=f'Média: {df_with_citations["citations"].mean():.1f}')
            plt.title('Distribuição de Citações')
            plt.xlabel('Número de Citações')
            plt.ylabel('Frequência')
            plt.legend()
            plt.tight_layout()
            plt.savefig(self.output_dir / 'citations_distribution.png', dpi=300, bbox_inches='tight')
            plt.show()

    def analyze_lotka_law(self) -> None:
       """Análise da Lei de Lotka (produtividade dos autores)."""
       print("📊 Analisando Lei de Lotka (produtividade dos autores)...")

       # Contar número de publicações por autor
       author_publications = defaultdict(int)
       for authors_row in self.df['authors'].dropna():
           authors_list = [a.strip() for a in str(authors_row).split(';')
                          if len(a.strip()) > 2]
           for author in authors_list:
               author_publications[author] += 1

       if not author_publications:
           print("   ⚠️ Dados insuficientes para análise de Lotka")
           return

       # Contar número de autores por número de publicações
       productivity_distribution = defaultdict(int)
       for author, count in author_publications.items():
           productivity_distribution[count] += 1

       # Ordenar por número de publicações
       sorted_distribution = sorted(productivity_distribution.items())

       # Preparar dados para gráfico
       publications_counts = [item[0] for item in sorted_distribution]
       authors_counts = [item[1] for item in sorted_distribution]

       # Gráfico da distribuição
       plt.figure(figsize=(12, 6))
       plt.bar(publications_counts, authors_counts, color='skyblue', alpha=0.7, edgecolor='black')
       plt.xlabel('Número de Publicações por Autor')
       plt.ylabel('Número de Autores')
       plt.title('Distribuição de Produtividade dos Autores (Lei de Lotka)')
       plt.yscale('log')
       plt.xscale('log')

       # Adicionar valores nas barras
       for i, (x, y) in enumerate(zip(publications_counts, authors_counts)):
           plt.text(x, y, str(y), ha='center', va='bottom')

       plt.tight_layout()
       plt.savefig(self.output_dir / 'lotka_law_analysis.png', dpi=300, bbox_inches='tight')
       plt.show()

       # Calcular ajuste para lei de Lotka (inverso do quadrado)
       if len(sorted_distribution) > 1:
           # Primeiro ponto como referência
           p1, a1 = sorted_distribution[0]
           expected_values = [a1 / (p ** 2) for p, _ in sorted_distribution]

           # Calcular coeficiente de determinação R²
           actual_values = [a for _, a in sorted_distribution]
           correlation_matrix = np.corrcoef(actual_values, expected_values)
           r_squared = correlation_matrix[0, 1] ** 2

           print(f"   🔍 Ajuste à Lei de Lotka (R²): {r_squared:.3f}")
           if r_squared > 0.7:
               print("   ✅ A distribuição segue aproximadamente a Lei de Lotka")
           else:
               print("   ⚠️ A distribuição não segue claramente a Lei de Lotka")

       print(f"   📊 Autores analisados: {len(author_publications)}")
       print(f"   📈 Publicações por autor (média): {np.mean(list(author_publications.values())):.2f}")

    def analyze_exponential_growth(self) -> None:
       """Análise de crescimento exponencial e modelos preditivos."""
       print("📈 Analisando crescimento exponencial e modelos preditivos...")

       # Obter dados de produção por ano
       yearly_production = self.df['year'].value_counts().sort_index()

       if len(yearly_production) < 3:
           print("   ⚠️ Dados insuficientes para análise de crescimento")
           return

       # Preparar dados para regressão
       years = np.array(yearly_production.index).reshape(-1, 1)
       publications = np.array(yearly_production.values)

       # Ajuste exponencial: y = a * e^(b*x)
       # Linearizar: ln(y) = ln(a) + b*x
       log_publications = np.log(publications)

       # Regressão linear nos dados linearizados
       from sklearn.linear_model import LinearRegression
       model = LinearRegression()
       model.fit(years, log_publications)

       # Parâmetros do modelo exponencial
       a = np.exp(model.intercept_)
       b = model.coef_[0]

       # Previsões
       future_years = np.arange(years.min(), years.max() + 6).reshape(-1, 1)  # 5 anos à frente
       predicted_log = model.predict(future_years)
       predicted_publications = np.exp(predicted_log)

       # Calcular R²
       r2_score = model.score(years, log_publications)

       print(f"   📊 Modelo de crescimento exponencial: y = {a:.2f} * e^({b:.3f}*x)")
       print(f"   🔍 Coeficiente de determinação (R²): {r2_score:.3f}")

       # Gráfico
       plt.figure(figsize=(12, 6))
       plt.scatter(years, publications, color='blue', s=100, label='Dados reais', zorder=3)
       plt.plot(future_years, predicted_publications, '--', color='red', linewidth=2,
               label=f'Modelo preditivo (R² = {r2_score:.3f})')

       # Preencher área de previsão
       split_index = len(years)
       plt.fill_between(future_years.flatten()[split_index-1:],
                       predicted_publications[split_index-1:],
                       alpha=0.3, color='red', label='Previsão')

       plt.xlabel('Ano')
       plt.ylabel('Número de Publicações')
       plt.title('Crescimento Exponencial e Previsão de Publicações')
       plt.legend()
       plt.grid(True, alpha=0.3)
       plt.tight_layout()
       plt.savefig(self.output_dir / 'exponential_growth_analysis.png', dpi=300, bbox_inches='tight')
       plt.show()

       # Calcular taxa de crescimento anual
       growth_rate = (np.exp(b) - 1) * 100
       print(f"   📈 Taxa de crescimento anual: {growth_rate:.2f}%")

       # Previsões para próximos anos
       print("   🔮 Previsões para próximos anos:")
       for i in range(len(years), len(future_years)):
           year = int(future_years[i][0])
           pred = int(predicted_publications[i])
           print(f"      {year}: ~{pred} publicações")

    def analyze_h_index(self) -> None:
       """Análise de impacto por H-index."""
       print("📊 Analisando impacto por H-index...")

       # Verificar se há dados de citações
       if 'citations' not in self.df.columns or self.df['citations'].sum() == 0:
           print("   ⚠️ Dados de citação não disponíveis")
           return

       # Calcular H-index para todos os artigos
       citations_sorted = sorted(self.df['citations'], reverse=True)
       h_index = 0
       for i, citations in enumerate(citations_sorted):
           if citations >= i + 1:
               h_index = i + 1
           else:
               break

       print(f"   📚 H-index geral: {h_index}")

       # Calcular H-index por autor
       author_citations = defaultdict(list)
       for _, row in self.df.iterrows():
           authors = str(row['authors']).split(';')
           citations = row['citations']
           for author in authors:
               author = author.strip()
               if len(author) > 2:
                   author_citations[author].append(citations)

       # Calcular H-index para cada autor
       author_h_index = {}
       for author, citations_list in author_citations.items():
           citations_sorted = sorted(citations_list, reverse=True)
           h_idx = 0
           for i, citations in enumerate(citations_sorted):
               if citations >= i + 1:
                   h_idx = i + 1
               else:
                   break
           author_h_index[author] = h_idx

       # Ordenar autores por H-index
       sorted_authors = sorted(author_h_index.items(), key=lambda x: x[1], reverse=True)

       # Mostrar top 15 autores por H-index
       print("   👥 Top 15 autores por H-index:")
       for i, (author, h_idx) in enumerate(sorted_authors[:15], 1):
           print(f"      {i:2d}. {author[:30]:<30} H-index: {h_idx}")

       # Gráfico de H-index dos top autores
       if sorted_authors:
           top_authors_data = sorted_authors[:15]
           authors_names = [author[:20] for author, _ in top_authors_data]
           h_indices = [h_idx for _, h_idx in top_authors_data]

           plt.figure(figsize=(12, 8))
           bars = plt.barh(range(len(authors_names)), h_indices, color='skyblue', alpha=0.7)
           plt.yticks(range(len(authors_names)), authors_names)
           plt.xlabel('H-index')
           plt.title('Top 15 Autores por H-index')
           plt.gca().invert_yaxis()  # Para mostrar o maior no topo

           # Adicionar valores nas barras
           for i, (bar, h_idx) in enumerate(zip(bars, h_indices)):
               plt.text(bar.get_width() + 0.1, bar.get_y() + bar.get_height()/2,
                       str(h_idx), ha='left', va='center')

           plt.tight_layout()
           plt.savefig(self.output_dir / 'h_index_analysis.png', dpi=300, bbox_inches='tight')
           plt.show()

       # Estatísticas gerais
       h_indices_all = list(author_h_index.values())
       if h_indices_all:
           print(f"   📊 Estatísticas de H-index:")
           print(f"      Média: {np.mean(h_indices_all):.2f}")
           print(f"      Mediana: {np.median(h_indices_all):.2f}")
           print(f"      Desvio padrão: {np.std(h_indices_all):.2f}")
           print(f"      Máximo: {np.max(h_indices_all)}")

    def analyze_international_collaboration(self) -> None:
       """Análise de colaboração internacional."""
       print("🌍 Analisando colaboração internacional...")

       # Verificar se há dados de afiliação de países
       if 'country_affiliation' not in self.df.columns:
           print("   ⚠️ Dados de afiliação de países não disponíveis")
           return

       # Contadores
       international_collaborations = 0
       domestic_collaborations = 0
       single_country_papers = 0
       total_papers_with_countries = 0

       # Análise por país
       country_publications = defaultdict(int)
       country_collaborations = defaultdict(int)
       collaboration_pairs = defaultdict(int)

       for _, row in self.df.iterrows():
           countries_str = str(row['country_affiliation'])
           if countries_str and countries_str not in ['nan', '']:
               countries = [c.strip() for c in countries_str.split(';') if c.strip()]
               if countries:
                   total_papers_with_countries += 1

                   # Contar publicações por país
                   for country in countries:
                       country_publications[country] += 1

                   # Analisar tipo de colaboração
                   unique_countries = list(set(countries))
                   if len(unique_countries) == 1:
                       single_country_papers += 1
                   else:
                       international_collaborations += 1
                       # Contar colaborações entre pares de países
                       for i in range(len(unique_countries)):
                           for j in range(i+1, len(unique_countries)):
                               pair = tuple(sorted([unique_countries[i], unique_countries[j]]))
                               collaboration_pairs[pair] += 1

                       # Contar colaborações por país
                       for country in unique_countries:
                           country_collaborations[country] += 1

       if total_papers_with_countries == 0:
           print("   ⚠️ Dados insuficientes para análise de colaboração internacional")
           return

       # Calcular percentuais
       international_rate = (international_collaborations / total_papers_with_countries) * 100
       single_country_rate = (single_country_papers / total_papers_with_countries) * 100

       print(f"   📊 Colaboração internacional: {international_rate:.1f}% dos artigos")
       print(f"   📊 Artigos de país único: {single_country_rate:.1f}% dos artigos")
       print(f"   📈 Total de artigos com dados de país: {total_papers_with_countries}")

       # Países mais produtivos
       top_countries = sorted(country_publications.items(), key=lambda x: x[1], reverse=True)[:10]
       print("   🌍 Top 10 países por produção:")
       for i, (country, count) in enumerate(top_countries, 1):
           collaboration_ratio = (country_collaborations[country] / count) * 100 if count > 0 else 0
           print(f"      {i:2d}. {country:<20} {count:>3} artigos ({collaboration_ratio:.1f}% colaboração)")

       # Pares de países mais colaborativos
       top_collaborations = sorted(collaboration_pairs.items(), key=lambda x: x[1], reverse=True)[:10]
       print("   🤝 Top 10 colaborações entre países:")
       for i, (pair, count) in enumerate(top_collaborations, 1):
           print(f"      {i:2d}. {pair[0]} - {pair[1]}: {count} artigos")

       # Gráfico de países mais produtivos
       if top_countries:
           countries_names = [country[:15] for country, _ in top_countries]
           publication_counts = [count for _, count in top_countries]
           collaboration_rates = [(country_collaborations[country] / country_publications[country]) * 100
                                 if country_publications[country] > 0 else 0
                                 for country, _ in top_countries]

           fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

           # Gráfico de barras de publicações
           bars1 = ax1.bar(range(len(countries_names)), publication_counts, color='skyblue', alpha=0.7)
           ax1.set_xlabel('Países')
           ax1.set_ylabel('Número de Publicações')
           ax1.set_title('Publicações por País')
           ax1.set_xticks(range(len(countries_names)))
           ax1.set_xticklabels(countries_names, rotation=45, ha='right')

           # Adicionar valores nas barras
           for bar, count in zip(bars1, publication_counts):
               ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                       str(count), ha='center', va='bottom')

           # Gráfico de taxa de colaboração
           bars2 = ax2.bar(range(len(countries_names)), collaboration_rates, color='lightcoral', alpha=0.7)
           ax2.set_xlabel('Países')
           ax2.set_ylabel('Taxa de Colaboração (%)')
           ax2.set_title('Taxa de Colaboração por País')
           ax2.set_xticks(range(len(countries_names)))
           ax2.set_xticklabels(countries_names, rotation=45, ha='right')
           ax2.set_ylim(0, 100)

           # Adicionar valores nas barras
           for bar, rate in zip(bars2, collaboration_rates):
               ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                       f'{rate:.1f}%', ha='center', va='bottom')

           plt.tight_layout()
           plt.savefig(self.output_dir / 'international_collaboration_analysis.png', dpi=300, bbox_inches='tight')
           plt.show()

    def analyze_top_countries_production(self) -> None:
       """Análise dos Top 15 Países por Produção Científica."""
       print("🌍 Analisando Top 15 Países por Produção Científica...")

       # Verificar se há dados de afiliação de países
       if 'country_affiliation' not in self.df.columns:
           print("   ⚠️ Dados de afiliação de países não disponíveis")
           return

       # Contar publicações por país
       country_publications = defaultdict(int)

       for _, row in self.df.iterrows():
           countries_str = str(row['country_affiliation'])
           if countries_str and countries_str not in ['nan', '']:
               countries = [c.strip() for c in countries_str.split(';') if c.strip()]
               # Para contagem de produção, contamos cada país envolvido
               for country in countries:
                   country_publications[country] += 1

       if not country_publications:
           print("   ⚠️ Dados insuficientes para análise de países")
           return

       # Ordenar países por número de publicações
       top_countries = sorted(country_publications.items(), key=lambda x: x[1], reverse=True)[:15]

       print("   🌍 Top 15 Países por Produção Científica:")
       for i, (country, count) in enumerate(top_countries, 1):
           print(f"      {i:2d}. {country:<20} {count:>3} artigos")

       # Criar DataFrame para facilitar manipulação
       df_countries = pd.DataFrame(top_countries, columns=['País', 'Publicações'])

       # Gráfico de barras dos top 15 países
       plt.figure(figsize=(14, 8))
       bars = plt.bar(range(len(df_countries)), df_countries['Publicações'],
                     color=plt.cm.viridis(np.linspace(0, 1, len(df_countries))),
                     alpha=0.8, edgecolor='black')

       plt.xlabel('Países')
       plt.ylabel('Número de Publicações')
       plt.title('Top 15 Países por Produção Científica')
       plt.xticks(range(len(df_countries)),
                 [country[:15] for country in df_countries['País']],
                 rotation=45, ha='right')

       # Adicionar valores nas barras
       for i, (bar, count) in enumerate(zip(bars, df_countries['Publicações'])):
           plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                   str(count), ha='center', va='bottom', fontweight='bold')

       plt.tight_layout()
       plt.savefig(self.output_dir / 'top_15_countries_production.png', dpi=300, bbox_inches='tight')
       plt.show()

       return df_countries

    def analyze_brazil_specific(self) -> None:
       """Análise específica da produção científica do Brasil."""
       print("🇧🇷 Analisando produção científica do Brasil...")

       # Verificar se há dados de afiliação de países
       if 'country_affiliation' not in self.df.columns:
           print("   ⚠️ Dados de afiliação de países não disponíveis")
           return

       # Filtrar artigos com afiliação do Brasil
       brazil_articles = []
       brazil_collaborations = []

       for _, row in self.df.iterrows():
           countries_str = str(row['country_affiliation'])
           if countries_str and countries_str not in ['nan', '']:
               countries = [c.strip() for c in countries_str.split(';') if c.strip()]
               # Verificar se Brasil está na lista de países
               if 'Brazil' in countries or 'BR' in countries:
                   brazil_articles.append(row)
                   # Verificar se é colaboração internacional
                   if len(set(countries)) > 1:
                       brazil_collaborations.append(row)

       if not brazil_articles:
           print("   ⚠️ Não foram encontrados artigos com afiliação brasileira")
           return

       # Converter para DataFrame
       df_brazil = pd.DataFrame(brazil_articles)
       df_brazil_collab = pd.DataFrame(brazil_collaborations)

       print(f"   📊 Produção científica do Brasil:")
       print(f"      Total de artigos: {len(df_brazil)}")
       print(f"      Colaborações internacionais: {len(df_brazil_collab)} ({len(df_brazil_collab)/len(df_brazil)*100:.1f}%)")

       # Análise por ano
       if 'year' in df_brazil.columns:
           brazil_yearly = df_brazil['year'].value_counts().sort_index()
           print(f"   📅 Evolução temporal:")
           for year, count in brazil_yearly.items():
               print(f"      {int(year)}: {count} artigos")

       # Análise por periódicos
       if 'journal' in df_brazil.columns:
           brazil_journals = df_brazil['journal'].value_counts().head(10)
           print(f"   📰 Principais periódicos:")
           for i, (journal, count) in enumerate(brazil_journals.items(), 1):
               print(f"      {i:2d}. {journal[:30]:<30} {count} artigos")

       # Análise de colaboração por país parceiro
       collaboration_countries = defaultdict(int)
       for _, row in df_brazil_collab.iterrows():
           countries_str = str(row['country_affiliation'])
           if countries_str and countries_str not in ['nan', '']:
               countries = [c.strip() for c in countries_str.split(';') if c.strip()]
               # Contar países parceiros (excluindo Brasil)
               for country in countries:
                   if country not in ['Brazil', 'BR']:
                       collaboration_countries[country] += 1

       if collaboration_countries:
           top_collaborations = sorted(collaboration_countries.items(), key=lambda x: x[1], reverse=True)[:10]
           print(f"   🤝 Principais países colaboradores:")
           for i, (country, count) in enumerate(top_collaborations, 1):
               print(f"      {i:2d}. {country:<20} {count} colaborações")

       # Criar gráfico de evolução temporal do Brasil
       if 'year' in df_brazil.columns:
           plt.figure(figsize=(12, 6))
           brazil_yearly_plot = df_brazil['year'].value_counts().sort_index()
           plt.plot(brazil_yearly_plot.index, brazil_yearly_plot.values,
                   marker='o', linewidth=2, markersize=8, color='green')
           plt.xlabel('Ano')
           plt.ylabel('Número de Publicações')
           plt.title('Evolução da Produção Científica do Brasil')
           plt.grid(True, alpha=0.3)

           # Adicionar valores nos pontos
           for year, count in brazil_yearly_plot.items():
               plt.annotate(str(count), (year, count), textcoords="offset points",
                           xytext=(0,10), ha='center')

           plt.tight_layout()
           plt.savefig(self.output_dir / 'brazil_production_evolution.png', dpi=300, bbox_inches='tight')
           plt.show()

       # Gráfico de colaborações internacionais
       if collaboration_countries:
           top_collab_list = sorted(collaboration_countries.items(), key=lambda x: x[1], reverse=True)[:10]
           countries_names = [country[:15] for country, _ in top_collab_list]
           collab_counts = [count for _, count in top_collab_list]

           plt.figure(figsize=(12, 6))
           bars = plt.bar(range(len(countries_names)), collab_counts,
                         color='orange', alpha=0.7, edgecolor='black')
           plt.xlabel('Países')
           plt.ylabel('Número de Colaborações')
           plt.title('Top 10 Países Colaboradores com o Brasil')
           plt.xticks(range(len(countries_names)), countries_names, rotation=45, ha='right')

           # Adicionar valores nas barras
           for bar, count in zip(bars, collab_counts):
               plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.3,
                       str(count), ha='center', va='bottom')

           plt.tight_layout()
           plt.savefig(self.output_dir / 'brazil_international_collaborations.png', dpi=300, bbox_inches='tight')
           plt.show()

       return df_brazil

    def analyze_thematic_diversity(self) -> None:
       """Análise de diversidade temática (Índice de Shannon)."""
       print("🏷️ Analisando diversidade temática (Índice de Shannon)...")

       # Verificar se há dados de palavras-chave
       if 'keywords' not in self.df.columns:
           print("   ⚠️ Dados de palavras-chave não disponíveis")
           return

       # Processar palavras-chave
       all_keywords = []
       paper_keywords = []  # Lista de palavras-chave por artigo

       for keywords_row in self.df['keywords'].dropna():
           if str(keywords_row).lower() not in ['nan', 'none', '']:
               individual_keywords = re.split(r'[;,]', str(keywords_row))
               clean_keywords = [k.strip().lower().title() for k in individual_keywords
                               if len(k.strip()) > 3 and not k.strip().isdigit()]
               if clean_keywords:
                   all_keywords.extend(clean_keywords)
                   paper_keywords.append(clean_keywords)

       if not all_keywords:
           print("   ⚠️ Dados insuficientes para análise temática")
           return

       # Calcular frequência de palavras-chave
       keyword_counts = Counter(all_keywords)
       total_keywords = len(all_keywords)

       # Calcular Índice de Shannon
       # H = -Σ(p_i * log(p_i)) onde p_i é a proporção da palavra-chave i
       shannon_index = 0
       for count in keyword_counts.values():
           p_i = count / total_keywords
           if p_i > 0:
               shannon_index -= p_i * np.log(p_i)

       # Normalizar o índice de Shannon (dividir por log(total de categorias))
       max_shannon = np.log(len(keyword_counts)) if len(keyword_counts) > 1 else 1
       normalized_shannon = shannon_index / max_shannon if max_shannon > 0 else 0

       print(f"   📊 Índice de Shannon: {shannon_index:.3f}")
       print(f"   📊 Índice de Shannon normalizado: {normalized_shannon:.3f}")
       print(f"   🏷️ Total de palavras-chave únicas: {len(keyword_counts)}")
       print(f"   📄 Total de ocorrências de palavras-chave: {total_keywords}")

       # Palavras-chave mais comuns
       top_keywords = keyword_counts.most_common(15)
       print("   🔤 Top 15 palavras-chave mais frequentes:")
       for i, (keyword, count) in enumerate(top_keywords, 1):
           percentage = (count / total_keywords) * 100
           print(f"      {i:2d}. {keyword:<25} {count:>4} ocorrências ({percentage:.1f}%)")

       # Calcular diversidade temática por ano
       yearly_diversity = {}
       yearly_data = {}

       # Associar palavras-chave com anos
       for _, row in self.df.iterrows():
           year = row['year']
           keywords_str = str(row['keywords'])
           if pd.notna(year) and keywords_str.lower() not in ['nan', 'none', '']:
               individual_keywords = re.split(r'[;,]', keywords_str)
               clean_keywords = [k.strip().lower().title() for k in individual_keywords
                               if len(k.strip()) > 3 and not k.strip().isdigit()]
               if clean_keywords:
                   if year not in yearly_data:
                       yearly_data[year] = []
                   yearly_data[year].extend(clean_keywords)

       # Calcular índice de Shannon para cada ano
       for year, keywords in yearly_data.items():
           if keywords:
               year_keyword_counts = Counter(keywords)
               total_year_keywords = len(keywords)
               year_shannon = 0
               for count in year_keyword_counts.values():
                   p_i = count / total_year_keywords
                   if p_i > 0:
                       year_shannon -= p_i * np.log(p_i)
               yearly_diversity[year] = year_shannon

       # Gráfico de evolução da diversidade temática
       if yearly_diversity:
           years = sorted(yearly_diversity.keys())
           diversity_values = [yearly_diversity[year] for year in years]

           plt.figure(figsize=(12, 6))
           plt.plot(years, diversity_values, marker='o', linewidth=2, markersize=8, color='blue')
           plt.xlabel('Ano')
           plt.ylabel('Índice de Shannon')
           plt.title('Evolução da Diversidade Temática (Índice de Shannon)')
           plt.grid(True, alpha=0.3)

           # Adicionar valores nos pontos
           for year, value in zip(years, diversity_values):
               plt.annotate(f'{value:.2f}', (year, value), textcoords="offset points",
                           xytext=(0,10), ha='center')

           plt.tight_layout()
           plt.savefig(self.output_dir / 'thematic_diversity_analysis.png', dpi=300, bbox_inches='tight')
           plt.show()

       # Gráfico das palavras-chave mais frequentes
       if top_keywords:
           keywords_names = [keyword for keyword, _ in top_keywords]
           keyword_counts_values = [count for _, count in top_keywords]

           plt.figure(figsize=(12, 8))
           bars = plt.barh(range(len(keywords_names)), keyword_counts_values, color='lightgreen', alpha=0.7)
           plt.yticks(range(len(keywords_names)), keywords_names)
           plt.xlabel('Número de Ocorrências')
           plt.title('Top 15 Palavras-Chave Mais Frequentes')
           plt.gca().invert_yaxis()  # Para mostrar a mais frequente no topo

           # Adicionar valores nas barras
           for i, (bar, count) in enumerate(zip(bars, keyword_counts_values)):
               plt.text(bar.get_width() + 0.5, bar.get_y() + bar.get_height()/2,
                       str(count), ha='left', va='center')

           plt.tight_layout()
           plt.savefig(self.output_dir / 'top_keywords_diversity.png', dpi=300, bbox_inches='tight')
           plt.show()

    def analyze_literature_obsolescence(self) -> None:
       """Análise de obsolescência da literatura (Índice de Price)."""
       print("🕰️ Analisando obsolescência da literatura (Índice de Price)...")

       # Verificar se há dados de ano e citações
       if 'year' not in self.df.columns or 'citations' not in self.df.columns:
           print("   ⚠️ Dados de ano ou citações não disponíveis")
           return

       current_year = datetime.now().year
       df_with_data = self.df.dropna(subset=['year', 'citations'])

       if len(df_with_data) == 0:
           print("   ⚠️ Dados insuficientes para análise de obsolescência")
           return

       # Calcular idade dos artigos
       df_with_data = df_with_data.copy()
       df_with_data['age'] = current_year - df_with_data['year']

       # Filtrar artigos com idade entre 0 e 20 anos (janela relevante)
       df_recent = df_with_data[(df_with_data['age'] >= 0) & (df_with_data['age'] <= 20)]

       if len(df_recent) == 0:
           print("   ⚠️ Dados insuficientes para análise de obsolescência")
           return

       # Calcular Índice de Price
       # Artigos considerados "atuais" (<=5 anos)
       current_papers = df_recent[df_recent['age'] <= 5]
       total_papers = len(df_recent)
       total_citations = df_recent['citations'].sum()
       current_citations = current_papers['citations'].sum()

       # Índice de Price: proporção de artigos/citações recentes
       price_paper_index = (len(current_papers) / total_papers) * 100 if total_papers > 0 else 0
       price_citation_index = (current_citations / total_citations) * 100 if total_citations > 0 else 0

       print(f"   📊 Período analisado: {int(df_recent['year'].min())} - {int(df_recent['year'].max())}")
       print(f"   📄 Total de artigos analisados: {total_papers}")
       print(f"   📚 Total de citações: {int(total_citations)}")
       print(f"   📊 Índice de Price (artigos): {price_paper_index:.1f}%")
       print(f"   📊 Índice de Price (citações): {price_citation_index:.1f}%")

       # Distribuição por idade
       age_groups = df_recent.groupby('age').agg({
           'title': 'count',
           'citations': 'sum'
       }).rename(columns={'title': 'papers', 'citations': 'citations'})

       # Calcular porcentagens
       age_groups['paper_percentage'] = (age_groups['papers'] / age_groups['papers'].sum()) * 100
       age_groups['citation_percentage'] = (age_groups['citations'] / age_groups['citations'].sum()) * 100

       print("   📊 Distribuição por idade:")
       for age in sorted(age_groups.index):
           papers = age_groups.loc[age, 'papers']
           citations = age_groups.loc[age, 'citations']
           paper_pct = age_groups.loc[age, 'paper_percentage']
           citation_pct = age_groups.loc[age, 'citation_percentage']
           print(f"      {age:2d} anos: {papers:>3d} artigos ({paper_pct:>5.1f}%) | {int(citations):>4d} citações ({citation_pct:>5.1f}%)")

       # Gráfico de distribuição por idade
       ages = sorted(age_groups.index)
       paper_counts = [age_groups.loc[age, 'papers'] for age in ages]
       citation_counts = [age_groups.loc[age, 'citations'] for age in ages]

       fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

       # Gráfico de barras para artigos
       bars1 = ax1.bar(ages, paper_counts, color='skyblue', alpha=0.7, edgecolor='black')
       ax1.set_xlabel('Idade dos Artigos (anos)')
       ax1.set_ylabel('Número de Artigos')
       ax1.set_title('Distribuição de Artigos por Idade')
       ax1.grid(True, alpha=0.3)

       # Adicionar valores nas barras
       for bar, count in zip(bars1, paper_counts):
           ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                   str(count), ha='center', va='bottom')

       # Gráfico de barras para citações
       bars2 = ax2.bar(ages, citation_counts, color='lightcoral', alpha=0.7, edgecolor='black')
       ax2.set_xlabel('Idade dos Artigos (anos)')
       ax2.set_ylabel('Número de Citações')
       ax2.set_title('Distribuição de Citações por Idade')
       ax2.grid(True, alpha=0.3)

       # Adicionar valores nas barras
       for bar, count in zip(bars2, citation_counts):
           ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                   str(int(count)), ha='center', va='bottom')

       plt.tight_layout()
       plt.savefig(self.output_dir / 'literature_obsolescence_analysis.png', dpi=300, bbox_inches='tight')
       plt.show()

       # Calcular meia-vida da literatura
       # Idade em que metade das citações foi acumulada
       cumulative_citations = age_groups['citations'].sort_index(ascending=False).cumsum()
       half_total_citations = total_citations / 2

       half_life = None
       for age in sorted(cumulative_citations.index, reverse=True):
           if cumulative_citations[age] >= half_total_citations:
               half_life = age
               break

       if half_life is not None:
           print(f"   ⏳ Meia-vida da literatura: {half_life} anos")
       else:
           print("   ⏳ Não foi possível calcular a meia-vida da literatura")

    def analyze_bradford_concentration(self) -> None:
       """Análise de concentração de Bradford."""
       print("📚 Analisando concentração de Bradford...")

       # Verificar se há dados de periódicos
       if 'journal' not in self.df.columns:
           print("   ⚠️ Dados de periódicos não disponíveis")
           return

       # Contar publicações por periódico
       journal_counts = self.df['journal'].value_counts()

       if len(journal_counts) == 0:
           print("   ⚠️ Dados insuficientes para análise de concentração")
           return

       # Ordenar periódicos por número de publicações
       sorted_journals = journal_counts.sort_values(ascending=False)
       total_papers = len(self.df)
       cumulative_papers = 0
       bradford_zones = []
       zone_number = 1
       papers_in_zone = 0

       # Lei de Bradford: dividir periódicos em zonas onde cada zona tem aproximadamente
       # o mesmo número de artigos, mas a zona 1 tem menos periódicos que as subsequentes
       # Zona 1: Periódicos de alta produtividade
       # Zona 2: Mais periódicos que a zona 1, mas mesmo número de artigos
       # Zona 3: Mais periódicos que a zona 2, mas mesmo número de artigos

       papers_per_zone = total_papers / 3  # Idealmente dividir em 3 zonas com mesmo número de artigos

       print(f"   📊 Total de artigos: {total_papers}")
       print(f"   📰 Total de periódicos: {len(sorted_journals)}")
       print(f"   📊 Artigos por zona de Bradford (ideal): {papers_per_zone:.0f}")

       # Classificar periódicos em zonas de Bradford
       current_zone_papers = 0
       zone_journals = []
       zone_info = []

       for journal, count in sorted_journals.items():
           cumulative_papers += count
           current_zone_papers += count
           papers_in_zone += count
           zone_journals.append(journal)

           # Verificar se completou uma zona (aproximadamente 1/3 dos artigos)
           if current_zone_papers >= papers_per_zone * 0.9 or journal == sorted_journals.index[-1]:
               zone_info.append({
                   'zone': zone_number,
                   'journals': zone_journals.copy(),
                   'journal_count': len(zone_journals),
                   'paper_count': current_zone_papers,
                   'cumulative_papers': cumulative_papers,
                   'percentage': (cumulative_papers / total_papers) * 100
               })

               # Imprimir informações da zona
               print(f"   📚 Zona {zone_number}: {len(zone_journals)} periódicos, {current_zone_papers} artigos")
               if zone_number <= 3:
                   print(f"      Principais periódicos:")
                   for i, journ in enumerate(zone_journals[:5], 1):
                       journ_count = journal_counts[journ]
                       print(f"         {i}. {journ[:30]:<30} ({journ_count} artigos)")

               # Preparar para próxima zona
               zone_number += 1
               current_zone_papers = 0
               zone_journals = []

               # Limitar a 5 zonas para evitar excesso de detalhes
               if zone_number > 5:
                   break

       # Se ainda houver periódicos não alocados, colocar na última zona
       if zone_journals and zone_number <= 5:
           zone_info.append({
               'zone': zone_number,
               'journals': zone_journals.copy(),
               'journal_count': len(zone_journals),
               'paper_count': current_zone_papers,
               'cumulative_papers': cumulative_papers,
               'percentage': (cumulative_papers / total_papers) * 100
           })
           print(f"   📚 Zona {zone_number}: {len(zone_journals)} periódicos, {current_zone_papers} artigos")

       # Calcular coeficiente de concentração de Bradford
       # Verificar relação entre número de periódicos e artigos por zona
       if len(zone_info) >= 2:
           # Comparar zona 1 com zona 2
           zone1_journals = zone_info[0]['journal_count']
           zone2_journals = zone_info[1]['journal_count'] if len(zone_info) > 1 else 0

           if zone1_journals > 0 and zone2_journals > 0:
               journal_ratio = zone2_journals / zone1_journals
               print(f"   📊 Razão de periódicos (Zona 2/Zona 1): {journal_ratio:.1f}:1")
               print(f"   🔍 Conformidade com Lei de Bradford: {journal_ratio:.1f}x periódicos na Zona 2")

       # Gráfico da distribuição de Bradford
       zones = [info['zone'] for info in zone_info]
       journal_counts_zones = [info['journal_count'] for info in zone_info]
       paper_counts_zones = [info['paper_count'] for info in zone_info]

       fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

       # Gráfico de barras para periódicos por zona
       bars1 = ax1.bar(zones, journal_counts_zones, color='skyblue', alpha=0.7, edgecolor='black')
       ax1.set_xlabel('Zonas de Bradford')
       ax1.set_ylabel('Número de Periódicos')
       ax1.set_title('Distribuição de Periódicos por Zona de Bradford')
       ax1.grid(True, alpha=0.3)

       # Adicionar valores nas barras
       for bar, count in zip(bars1, journal_counts_zones):
           ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                   str(count), ha='center', va='bottom')

       # Gráfico de barras para artigos por zona
       bars2 = ax2.bar(zones, paper_counts_zones, color='lightcoral', alpha=0.7, edgecolor='black')
       ax2.set_xlabel('Zonas de Bradford')
       ax2.set_ylabel('Número de Artigos')
       ax2.set_title('Distribuição de Artigos por Zona de Bradford')
       ax2.grid(True, alpha=0.3)

       # Adicionar valores nas barras
       for bar, count in zip(bars2, paper_counts_zones):
           ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                   str(int(count)), ha='center', va='bottom')

       plt.tight_layout()
       plt.savefig(self.output_dir / 'bradford_concentration_analysis.png', dpi=300, bbox_inches='tight')
       plt.show()

       # Calcular índice de Gini para concentração de periódicos
       # Converter para array numpy
       journal_counts_array = np.array(sorted_journals.values)
       n = len(journal_counts_array)

       if n > 1:
           # Calcular índice de Gini
           index = np.arange(1, n + 1)
           gini_numerator = np.sum((2 * index - n - 1) * journal_counts_array)
           gini_denominator = n * np.sum(journal_counts_array)
           gini_coefficient = gini_numerator / gini_denominator

           print(f"   📊 Índice de Gini de concentração: {gini_coefficient:.3f}")
           if gini_coefficient > 0.6:
               print("   ⚠️ Alta concentração de publicações em poucos periódicos")
           elif gini_coefficient > 0.4:
               print("   📊 Concentração moderada de publicações")
           else:
               print("   ✅ Distribuição relativamente equitativa de publicações")

    def analyze_keyword_cooccurrence(self) -> None:
       """Análise de coocorrência de palavras-chave."""
       print("🔗 Analisando coocorrência de palavras-chave...")

       # Verificar se há dados de palavras-chave
       if 'keywords' not in self.df.columns:
           print("   ⚠️ Dados de palavras-chave não disponíveis")
           return

       # Processar palavras-chave
       paper_keywords = []  # Lista de palavras-chave por artigo
       all_keywords = []

       for keywords_row in self.df['keywords'].dropna():
           if str(keywords_row).lower() not in ['nan', 'none', '']:
               individual_keywords = re.split(r'[;,]', str(keywords_row))
               clean_keywords = [k.strip().lower().title() for k in individual_keywords
                               if len(k.strip()) > 3 and not k.strip().isdigit()]
               if clean_keywords:
                   paper_keywords.append(clean_keywords)
                   all_keywords.extend(clean_keywords)

       if not paper_keywords:
           print("   ⚠️ Dados insuficientes para análise de coocorrência")
           return

       # Criar vocabulário de palavras-chave
       keyword_vocab = list(set(all_keywords))
       vocab_size = len(keyword_vocab)
       keyword_to_index = {keyword: i for i, keyword in enumerate(keyword_vocab)}

       print(f"   🏷️ Total de palavras-chave únicas: {vocab_size}")
       print(f"   📄 Artigos com palavras-chave: {len(paper_keywords)}")

       # Construir matriz de coocorrência
       cooccurrence_matrix = np.zeros((vocab_size, vocab_size), dtype=int)

       # Preencher matriz de coocorrência
       for keywords_list in paper_keywords:
           # Obter índices das palavras-chave neste artigo
           indices = [keyword_to_index[keyword] for keyword in keywords_list if keyword in keyword_to_index]
           # Incrementar contagem para cada par de palavras-chave
           for i in range(len(indices)):
               for j in range(i+1, len(indices)):
                   idx1, idx2 = indices[i], indices[j]
                   cooccurrence_matrix[idx1, idx2] += 1
                   cooccurrence_matrix[idx2, idx1] += 1  # Matriz simétrica

       # Identificar pares de palavras-chave mais coocorrentes
       keyword_pairs = []
       for i in range(vocab_size):
           for j in range(i+1, vocab_size):
               if cooccurrence_matrix[i, j] > 0:
                   keyword_pairs.append((keyword_vocab[i], keyword_vocab[j], cooccurrence_matrix[i, j]))

       # Ordenar pares por frequência de coocorrência
       keyword_pairs.sort(key=lambda x: x[2], reverse=True)

       # Mostrar top 20 pares de coocorrência
       print("   🔗 Top 20 pares de palavras-chave coocorrentes:")
       for i, (kw1, kw2, count) in enumerate(keyword_pairs[:20], 1):
           print(f"      {i:2d}. '{kw1}' + '{kw2}': {count} coocorrências")

       # Encontrar palavras-chave mais centrais (alto grau de coocorrência)
       keyword_degrees = np.sum(cooccurrence_matrix > 0, axis=1)  # Número de outras palavras-chave com que cada uma coocorre
       keyword_total_cooc = np.sum(cooccurrence_matrix, axis=1)   # Total de coocorrências

       # Criar dataframe para análise
       keyword_stats = pd.DataFrame({
           'keyword': keyword_vocab,
           'degree': keyword_degrees,
           'total_cooccurrences': keyword_total_cooc
       }).sort_values('degree', ascending=False)

       print("   🌟 Top 15 palavras-chave mais conectadas:")
       for i, (_, row) in enumerate(keyword_stats.head(15).iterrows(), 1):
           print(f"      {i:2d}. {row['keyword']:<25} Conecta com {row['degree']:>2d} outras ({row['total_cooccurrences']:>3d} coocorrências)")

       # Gráfico de rede de coocorrência (para top palavras-chave)
       if len(keyword_pairs) > 0:
           # Criar grafo
           G = nx.Graph()

           # Adicionar nós (top 30 palavras-chave por grau)
           top_keywords_for_network = keyword_stats.head(30)
           for _, row in top_keywords_for_network.iterrows():
               G.add_node(row['keyword'], degree=row['degree'], total_cooc=row['total_cooccurrences'])

           # Adicionar arestas (pares com alta coocorrência)
           top_pairs_for_network = keyword_pairs[:50]  # Top 50 pares
           for kw1, kw2, count in top_pairs_for_network:
               if kw1 in G.nodes() and kw2 in G.nodes():
                   G.add_edge(kw1, kw2, weight=count)

           # Visualizar rede se houver nós e arestas
           if len(G.nodes()) > 2 and len(G.edges()) > 0:
               plt.figure(figsize=(16, 12))

               # Layout
               pos = nx.spring_layout(G, k=2, iterations=50)

               # Tamanhos dos nós baseados no grau
               node_sizes = [G.nodes[node]['degree'] * 100 for node in G.nodes()]

               # Cores dos nós baseadas no total de coocorrências
               node_colors = [G.nodes[node]['total_cooc'] for node in G.nodes()]

               # Desenhar nós
               nodes = nx.draw_networkx_nodes(G, pos, node_size=node_sizes,
                                             node_color=node_colors, cmap=plt.cm.viridis,
                                             alpha=0.7)

               # Desenhar arestas
               edge_widths = [G.edges[edge]['weight'] / 2 for edge in G.edges()]
               nx.draw_networkx_edges(G, pos, width=edge_widths, alpha=0.5, edge_color='gray')

               # Desenhar labels
               nx.draw_networkx_labels(G, pos, font_size=10, font_weight='bold')

               plt.title('Rede de Coocorrência de Palavras-Chave\n(Tamanho do nó = conectividade, Cor = total de coocorrências)')
               plt.axis('off')
               plt.colorbar(nodes, ax=plt.gca(), label='Total de Coocorrências')

               plt.tight_layout()
               plt.savefig(self.output_dir / 'keyword_cooccurrence_network.png', dpi=300, bbox_inches='tight')
               plt.show()

               # Estatísticas da rede
               print(f"   📊 Estatísticas da rede de coocorrência:")
               print(f"      Nós (palavras-chave): {G.number_of_nodes()}")
               print(f"      Arestas (coocorrências): {G.number_of_edges()}")
               print(f"      Densidade da rede: {nx.density(G):.4f}")
               if len(G.nodes()) > 1:
                   print(f"      Centralidade média: {np.mean(list(nx.degree_centrality(G).values())):.4f}")

       # Matriz de coocorrência (heatmap para top 20 palavras-chave)
       if vocab_size > 1:
           # Selecionar top 20 palavras-chave por grau
           top_keywords_matrix = keyword_stats.head(20)
           top_indices = [keyword_to_index[kw] for kw in top_keywords_matrix['keyword']]

           # Criar submatriz
           cooc_matrix_subset = cooccurrence_matrix[np.ix_(top_indices, top_indices)]

           # Criar heatmap
           plt.figure(figsize=(12, 10))
           sns.heatmap(cooc_matrix_subset,
                      xticklabels=[kw[:15] for kw in top_keywords_matrix['keyword']],
                      yticklabels=[kw[:15] for kw in top_keywords_matrix['keyword']],
                      annot=True, fmt='d', cmap='YlOrRd', cbar=True)
           plt.title('Matriz de Coocorrência - Top 20 Palavras-Chave')
           plt.xlabel('Palavras-Chave')
           plt.ylabel('Palavras-Chave')
           plt.xticks(rotation=45, ha='right')
           plt.yticks(rotation=0)
           plt.tight_layout()
           plt.savefig(self.output_dir / 'keyword_cooccurrence_heatmap.png', dpi=300, bbox_inches='tight')
           plt.show()

    def analyze_citation_window(self) -> None:
       """Análise da janela de citação."""
       print("📚 Analisando janela de citação...")

       # Verificar se há dados de ano e citações
       if 'year' not in self.df.columns or 'citations' not in self.df.columns:
           print("   ⚠️ Dados de ano ou citações não disponíveis")
           return

       current_year = datetime.now().year
       df_with_data = self.df.dropna(subset=['year', 'citations'])

       if len(df_with_data) == 0:
           print("   ⚠️ Dados insuficientes para análise de janela de citação")
           return

       # Calcular idade dos artigos
       df_with_data = df_with_data.copy()
       df_with_data['age'] = current_year - df_with_data['year']

       # Filtrar artigos com idade entre 1 e 15 anos (janela relevante para citações)
       df_citable = df_with_data[(df_with_data['age'] >= 1) & (df_with_data['age'] <= 15)]

       if len(df_citable) == 0:
           print("   ⚠️ Dados insuficientes para análise de janela de citação")
           return

       print(f"   📊 Período analisado: {int(df_citable['year'].min())} - {int(df_citable['year'].max())}")
       print(f"   📄 Artigos analisáveis: {len(df_citable)}")
       print(f"   📚 Total de citações: {int(df_citable['citations'].sum())}")

       # Calcular citações por idade
       age_citation_stats = df_citable.groupby('age').agg({
           'title': 'count',
           'citations': ['sum', 'mean', 'median']
       }).round(2)

       # Achatar colunas
       age_citation_stats.columns = ['paper_count', 'total_citations', 'mean_citations', 'median_citations']

       print("   📊 Citações por idade dos artigos:")
       for age in sorted(age_citation_stats.index):
           paper_count = age_citation_stats.loc[age, 'paper_count']
           total_citations = age_citation_stats.loc[age, 'total_citations']
           mean_citations = age_citation_stats.loc[age, 'mean_citations']
           median_citations = age_citation_stats.loc[age, 'median_citations']
           print(f"      {age:2d} anos: {paper_count:>3d} artigos | {int(total_citations):>4d} citações | "
                 f"Média: {mean_citations:>5.1f} | Mediana: {median_citations:>5.1f}")

       # Identificar pico de citação (idade com maior média de citações)
       peak_age = age_citation_stats['mean_citations'].idxmax()
       peak_citations = age_citation_stats.loc[peak_age, 'mean_citations']
       print(f"   📈 Pico de citação: {peak_age} anos após publicação (média: {peak_citations:.1f} citações)")

       # Calcular tempo para atingir 50% das citações totais (meia-vida de citação)
       total_citations = df_citable['citations'].sum()
       half_total = total_citations / 2
       cumulative_citations = 0
       citation_half_life = None

       # Ordenar por idade (do mais novo para o mais antigo)
       for age in sorted(df_citable['age'].unique(), reverse=True):
           age_citations = df_citable[df_citable['age'] == age]['citations'].sum()
           cumulative_citations += age_citations
           if cumulative_citations >= half_total:
               citation_half_life = age
               break

       if citation_half_life is not None:
           print(f"   ⏳ Meia-vida de citação: {citation_half_life} anos")
       else:
           print("   ⏳ Não foi possível calcular a meia-vida de citação")

       # Calcular índice de imediação (proporção de citações nos primeiros 5 anos)
       early_citations = df_citable[df_citable['age'] <= 5]['citations'].sum()
       immediacy_index = (early_citations / total_citations) * 100 if total_citations > 0 else 0
       print(f"   📊 Índice de imediação (5 anos): {immediacy_index:.1f}%")

       # Gráfico de citações por idade
       ages = sorted(age_citation_stats.index)
       paper_counts = [age_citation_stats.loc[age, 'paper_count'] for age in ages]
       mean_citations = [age_citation_stats.loc[age, 'mean_citations'] for age in ages]
       total_citations_list = [age_citation_stats.loc[age, 'total_citations'] for age in ages]

       fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

       # Gráfico de barras para número de artigos por idade
       bars1 = ax1.bar(ages, paper_counts, color='skyblue', alpha=0.7, edgecolor='black')
       ax1.set_xlabel('Idade dos Artigos (anos)')
       ax1.set_ylabel('Número de Artigos')
       ax1.set_title('Número de Artigos por Idade')
       ax1.grid(True, alpha=0.3)

       # Adicionar valores nas barras
       for bar, count in zip(bars1, paper_counts):
           ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                   str(count), ha='center', va='bottom')

       # Gráfico de linha para média de citações por idade
       line2 = ax2.plot(ages, mean_citations, marker='o', linewidth=2, markersize=8, color='red')
       ax2.set_xlabel('Idade dos Artigos (anos)')
       ax2.set_ylabel('Média de Citações')
       ax2.set_title('Média de Citações por Idade dos Artigos')
       ax2.grid(True, alpha=0.3)

       # Adicionar valores nos pontos
       for age, mean_cit in zip(ages, mean_citations):
           ax2.annotate(f'{mean_cit:.1f}', (age, mean_cit), textcoords="offset points",
                       xytext=(0,10), ha='center')

       plt.tight_layout()
       plt.savefig(self.output_dir / 'citation_window_analysis.png', dpi=300, bbox_inches='tight')
       plt.show()

       # Análise de artigos com alto impacto (acima da média de citações)
       overall_mean = df_citable['citations'].mean()
       high_impact_papers = df_citable[df_citable['citations'] > overall_mean]

       if len(high_impact_papers) > 0:
           print(f"   🌟 Artigos de alto impacto (> {overall_mean:.1f} citações):")
           print(f"      Total: {len(high_impact_papers)} artigos ({(len(high_impact_papers)/len(df_citable)*100):.1f}%)")

           # Distribuição por idade dos artigos de alto impacto
           high_impact_by_age = high_impact_papers['age'].value_counts().sort_index()
           print("      Distribuição por idade:")
           for age in high_impact_by_age.index:
               count = high_impact_by_age[age]
               percentage = (count / len(high_impact_papers)) * 100
               print(f"         {age:2d} anos: {count:>3d} artigos ({percentage:>5.1f}%)")

    def analyze_emerging_trends(self) -> None:
       """Análise de tendências emergentes por N-grams."""
       print("🔍 Analisando tendências emergentes por N-grams...")

       # Verificar se há dados de título e abstract
       if 'title' not in self.df.columns and 'abstract' not in self.df.columns:
           print("   ⚠️ Dados de título ou abstract não disponíveis")
           return

       # Combinar títulos e abstracts
       texts = []
       for _, row in self.df.iterrows():
           text = ""
           if 'title' in self.df.columns and pd.notna(row['title']):
               text += str(row['title']) + " "
           if 'abstract' in self.df.columns and pd.notna(row['abstract']):
               text += str(row['abstract'])
           if text.strip():
               texts.append(text.strip())

       if not texts:
           print("   ⚠️ Dados insuficientes para análise de tendências")
           return

       print(f"   📄 Textos analisados: {len(texts)}")

       # Pré-processamento de textos
       def preprocess_text(text):
           # Converter para minúsculas
           text = text.lower()
           # Remover pontuação e números
           text = re.sub(r'[^a-zA-Z\s]', '', text)
           # Remover espaços extras
           text = re.sub(r'\s+', ' ', text).strip()
           return text

       # Pré-processar textos
       processed_texts = [preprocess_text(text) for text in texts]

       # Análise por ano para identificar tendências emergentes
       if 'year' in self.df.columns:
           df_with_year = self.df.dropna(subset=['year'])
           year_text_mapping = {}

           for _, row in df_with_year.iterrows():
               year = int(row['year'])
               text = ""
               if pd.notna(row['title']):
                   text += str(row['title']) + " "
               if pd.notna(row['abstract']):
                   text += str(row['abstract'])
               if text.strip():
                   if year not in year_text_mapping:
                       year_text_mapping[year] = []
                   year_text_mapping[year].append(preprocess_text(text))

           # Analisar N-grams por ano
           from sklearn.feature_extraction.text import CountVectorizer

           years = sorted(year_text_mapping.keys())
           if len(years) < 2:
               print("   ⚠️ Dados insuficientes para análise temporal de tendências")
               return

           print(f"   📊 Período analisado: {min(years)} - {max(years)}")

           # Extrair N-grams (unigramas, bigramas e trigramas)
           ngram_ranges = [(1, 1), (2, 2), (3, 3)]
           ngram_labels = ['Unigramas', 'Bigramas', 'Trigramas']

           trend_analysis = {}

           for (ngram_range, label) in zip(ngram_ranges, ngram_labels):
               print(f"   🔍 Analisando {label.lower()}...")

               # Vectorizer para o período completo
               vectorizer = CountVectorizer(
                   ngram_range=ngram_range,
                   max_features=1000,
                   stop_words='english',
                   min_df=2  # Aparecer em pelo menos 2 documentos
               )

               # Combinar todos os textos para obter vocabulário completo
               all_texts = []
               for year_texts in year_text_mapping.values():
                   all_texts.extend(year_texts)

               try:
                   vectorizer.fit(all_texts)
                   feature_names = vectorizer.get_feature_names_out()
               except ValueError:
                   print(f"   ⚠️ Dados insuficientes para análise de {label.lower()}")
                   continue

               # Calcular frequência por ano
               yearly_ngram_freq = {}
               for year in years:
                   if year in year_text_mapping:
                       year_texts = year_text_mapping[year]
                       if year_texts:
                           try:
                               year_matrix = vectorizer.transform(year_texts)
                               # Soma das frequências de cada n-grama neste ano
                               ngram_sums = np.array(year_matrix.sum(axis=0)).flatten()
                               yearly_ngram_freq[year] = ngram_sums
                           except:
                               yearly_ngram_freq[year] = np.zeros(len(feature_names))
                       else:
                           yearly_ngram_freq[year] = np.zeros(len(feature_names))
                   else:
                       yearly_ngram_freq[year] = np.zeros(len(feature_names))

               # Calcular taxa de crescimento para cada n-grama
               ngram_growth = {}
               for i, ngram in enumerate(feature_names):
                   # Obter frequências nos últimos 3 anos disponíveis
                   recent_years = years[-3:] if len(years) >= 3 else years
                   if len(recent_years) < 2:
                       continue

                   recent_frequencies = [yearly_ngram_freq[year][i] for year in recent_years]

                   # Calcular taxa de crescimento (regressão linear)
                   if sum(recent_frequencies) > 0:
                       x = np.array(range(len(recent_years))).reshape(-1, 1)
                       y = np.array(recent_frequencies)

                       # Apenas calcular crescimento se houver variação
                       if len(set(y)) > 1:
                           from sklearn.linear_model import LinearRegression
                           model = LinearRegression()
                           model.fit(x, y)
                           growth_rate = model.coef_[0]

                           # Calcular importância relativa (frequência total)
                           total_freq = sum(recent_frequencies)

                           # Score combinado: crescimento + frequência
                           combined_score = growth_rate * np.log(total_freq + 1)

                           if combined_score > 0:  # Apenas tendências crescentes
                               ngram_growth[ngram] = {
                                   'growth_rate': growth_rate,
                                   'total_frequency': total_freq,
                                   'recent_frequencies': recent_frequencies,
                                   'combined_score': combined_score
                               }

               # Ordenar por score combinado
               sorted_ngrams = sorted(ngram_growth.items(), key=lambda x: x[1]['combined_score'], reverse=True)

               if sorted_ngrams:
                   trend_analysis[label] = sorted_ngrams[:10]  # Top 10 tendências
                   print(f"   📈 Top 10 {label.lower()} emergentes:")
                   for i, (ngram, stats) in enumerate(sorted_ngrams[:10], 1):
                       growth = stats['growth_rate']
                       freq = stats['total_frequency']
                       print(f"      {i:2d}. '{ngram}' - Crescimento: {growth:>6.2f}, Frequência: {freq:>4.0f}")

           # Gráfico de evolução das top tendências (unigramas)
           if 'Unigramas' in trend_analysis and trend_analysis['Unigramas']:
               top_unigrams = trend_analysis['Unigramas'][:5]  # Top 5 unigramas

               plt.figure(figsize=(12, 8))

               for ngram, stats in top_unigrams:
                   # Recriar dados para plotagem
                   recent_years = years[-3:] if len(years) >= 3 else years
                   frequencies = stats['recent_frequencies'][-len(recent_years):]
                   plt.plot(recent_years, frequencies, marker='o', linewidth=2, markersize=6, label=f"'{ngram}'")

               plt.xlabel('Ano')
               plt.ylabel('Frequência')
               plt.title('Evolução das Principais Tendências (Unigramas)')
               plt.legend()
               plt.grid(True, alpha=0.3)
               plt.tight_layout()
               plt.savefig(self.output_dir / 'emerging_trends_analysis.png', dpi=300, bbox_inches='tight')
               plt.show()

           # Identificar lacunas temáticas (n-grams com pouca presença)
           print("   🔍 Identificando lacunas temáticas...")
           all_processed_texts = ' '.join(processed_texts)

           # Vectorizer para identificar n-grams raros
           rare_vectorizer = CountVectorizer(
               ngram_range=(1, 2),  # Unigramas e bigramas
               max_features=500,
               stop_words='english',
               min_df=1,   # Aparecer em pelo menos 1 documento
               max_df=0.1  # Aparecer em no máximo 10% dos documentos
           )

           try:
               rare_matrix = rare_vectorizer.fit_transform(processed_texts)
               rare_features = rare_vectorizer.get_feature_names_out()
               rare_frequencies = np.array(rare_matrix.sum(axis=0)).flatten()

               # Identificar n-grams com baixa frequência
               rare_ngrams = [(rare_features[i], rare_frequencies[i])
                             for i in range(len(rare_features))
                             if rare_frequencies[i] > 0]
               rare_ngrams.sort(key=lambda x: x[1])  # Ordenar por frequência crescente

               if rare_ngrams:
                   print("   🌱 Top 10 lacunas temáticas (n-grams sub-representados):")
                   for i, (ngram, freq) in enumerate(rare_ngrams[:10], 1):
                       print(f"      {i:2d}. '{ngram}' - Frequência: {freq}")
           except:
               print("   ⚠️ Não foi possível identificar lacunas temáticas")

       else:
           print("   ⚠️ Dados de ano não disponíveis para análise temporal")

       # Análise geral de N-grams mais frequentes
       print("   📊 N-grams mais frequentes no corpus completo:")
       from sklearn.feature_extraction.text import CountVectorizer

       # Unigramas mais frequentes
       unigram_vectorizer = CountVectorizer(
           ngram_range=(1, 1),
           max_features=15,
           stop_words='english'
       )

       try:
           unigram_matrix = unigram_vectorizer.fit_transform(processed_texts)
           unigram_features = unigram_vectorizer.get_feature_names_out()
           unigram_frequencies = np.array(unigram_matrix.sum(axis=0)).flatten()

           # Ordenar por frequência
           unigram_pairs = list(zip(unigram_features, unigram_frequencies))
           unigram_pairs.sort(key=lambda x: x[1], reverse=True)

           print("   🔤 Unigramas mais frequentes:")
           for i, (ngram, freq) in enumerate(unigram_pairs, 1):
               print(f"      {i:2d}. '{ngram}' - Frequência: {int(freq)}")
       except:
           print("   ⚠️ Não foi possível calcular unigramas mais frequentes")

       # Verificar se há dados de ano e citações
       if 'year' not in self.df.columns or 'citations' not in self.df.columns:
           print("   ⚠️ Dados de ano ou citações não disponíveis")
           return

       current_year = datetime.now().year
       df_with_data = self.df.dropna(subset=['year', 'citations'])

       if len(df_with_data) == 0:
           print("   ⚠️ Dados insuficientes para análise de janela de citação")
           return

       # Calcular idade dos artigos
       df_with_data = df_with_data.copy()
       df_with_data['age'] = current_year - df_with_data['year']

       # Filtrar artigos com idade entre 1 e 15 anos (janela relevante para citações)
       df_citable = df_with_data[(df_with_data['age'] >= 1) & (df_with_data['age'] <= 15)]

       if len(df_citable) == 0:
           print("   ⚠️ Dados insuficientes para análise de janela de citação")
           return

       print(f"   📊 Período analisado: {int(df_citable['year'].min())} - {int(df_citable['year'].max())}")
       print(f"   📄 Artigos analisáveis: {len(df_citable)}")
       print(f"   📚 Total de citações: {int(df_citable['citations'].sum())}")

       # Calcular citações por idade
       age_citation_stats = df_citable.groupby('age').agg({
           'title': 'count',
           'citations': ['sum', 'mean', 'median']
       }).round(2)

       # Achatar colunas
       age_citation_stats.columns = ['paper_count', 'total_citations', 'mean_citations', 'median_citations']

       print("   📊 Citações por idade dos artigos:")
       for age in sorted(age_citation_stats.index):
           paper_count = age_citation_stats.loc[age, 'paper_count']
           total_citations = age_citation_stats.loc[age, 'total_citations']
           mean_citations = age_citation_stats.loc[age, 'mean_citations']
           median_citations = age_citation_stats.loc[age, 'median_citations']
           print(f"      {age:2d} anos: {paper_count:>3d} artigos | {int(total_citations):>4d} citações | "
                 f"Média: {mean_citations:>5.1f} | Mediana: {median_citations:>5.1f}")

       # Identificar pico de citação (idade com maior média de citações)
       peak_age = age_citation_stats['mean_citations'].idxmax()
       peak_citations = age_citation_stats.loc[peak_age, 'mean_citations']
       print(f"   📈 Pico de citação: {peak_age} anos após publicação (média: {peak_citations:.1f} citações)")

       # Calcular tempo para atingir 50% das citações totais (meia-vida de citação)
       total_citations = df_citable['citations'].sum()
       half_total = total_citations / 2
       cumulative_citations = 0
       citation_half_life = None

       # Ordenar por idade (do mais novo para o mais antigo)
       for age in sorted(df_citable['age'].unique(), reverse=True):
           age_citations = df_citable[df_citable['age'] == age]['citations'].sum()
           cumulative_citations += age_citations
           if cumulative_citations >= half_total:
               citation_half_life = age
               break

       if citation_half_life is not None:
           print(f"   ⏳ Meia-vida de citação: {citation_half_life} anos")
       else:
           print("   ⏳ Não foi possível calcular a meia-vida de citação")

       # Calcular índice de imediação (proporção de citações nos primeiros 5 anos)
       early_citations = df_citable[df_citable['age'] <= 5]['citations'].sum()
       immediacy_index = (early_citations / total_citations) * 100 if total_citations > 0 else 0
       print(f"   📊 Índice de imediação (5 anos): {immediacy_index:.1f}%")

       # Gráfico de citações por idade
       ages = sorted(age_citation_stats.index)
       paper_counts = [age_citation_stats.loc[age, 'paper_count'] for age in ages]
       mean_citations = [age_citation_stats.loc[age, 'mean_citations'] for age in ages]
       total_citations_list = [age_citation_stats.loc[age, 'total_citations'] for age in ages]

       fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

       # Gráfico de barras para número de artigos por idade
       bars1 = ax1.bar(ages, paper_counts, color='skyblue', alpha=0.7, edgecolor='black')
       ax1.set_xlabel('Idade dos Artigos (anos)')
       ax1.set_ylabel('Número de Artigos')
       ax1.set_title('Número de Artigos por Idade')
       ax1.grid(True, alpha=0.3)

       # Adicionar valores nas barras
       for bar, count in zip(bars1, paper_counts):
           ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                   str(count), ha='center', va='bottom')

       # Gráfico de linha para média de citações por idade
       line2 = ax2.plot(ages, mean_citations, marker='o', linewidth=2, markersize=8, color='red')
       ax2.set_xlabel('Idade dos Artigos (anos)')
       ax2.set_ylabel('Média de Citações')
       ax2.set_title('Média de Citações por Idade dos Artigos')
       ax2.grid(True, alpha=0.3)

       # Adicionar valores nos pontos
       for age, mean_cit in zip(ages, mean_citations):
           ax2.annotate(f'{mean_cit:.1f}', (age, mean_cit), textcoords="offset points",
                       xytext=(0,10), ha='center')

       plt.tight_layout()
       plt.savefig(self.output_dir / 'citation_window_analysis.png', dpi=300, bbox_inches='tight')
       plt.show()

       # Análise de artigos com alto impacto (acima da média de citações)
       overall_mean = df_citable['citations'].mean()
       high_impact_papers = df_citable[df_citable['citations'] > overall_mean]

       if len(high_impact_papers) > 0:
           print(f"   🌟 Artigos de alto impacto (> {overall_mean:.1f} citações):")
           print(f"      Total: {len(high_impact_papers)} artigos ({(len(high_impact_papers)/len(df_citable)*100):.1f}%)")

           # Distribuição por idade dos artigos de alto impacto
           high_impact_by_age = high_impact_papers['age'].value_counts().sort_index()
           print("      Distribuição por idade:")
           for age in high_impact_by_age.index:
               count = high_impact_by_age[age]
               percentage = (count / len(high_impact_papers)) * 100
               print(f"         {age:2d} anos: {count:>3d} artigos ({percentage:>5.1f}%)")

       # Verificar se há dados de palavras-chave
       if 'keywords' not in self.df.columns:
           print("   ⚠️ Dados de palavras-chave não disponíveis")
           return

       # Processar palavras-chave
       paper_keywords = []  # Lista de palavras-chave por artigo
       all_keywords = []

       for keywords_row in self.df['keywords'].dropna():
           if str(keywords_row).lower() not in ['nan', 'none', '']:
               individual_keywords = re.split(r'[;,]', str(keywords_row))
               clean_keywords = [k.strip().lower().title() for k in individual_keywords
                               if len(k.strip()) > 3 and not k.strip().isdigit()]
               if clean_keywords:
                   paper_keywords.append(clean_keywords)
                   all_keywords.extend(clean_keywords)

       if not paper_keywords:
           print("   ⚠️ Dados insuficientes para análise de coocorrência")
           return

       # Criar vocabulário de palavras-chave
       keyword_vocab = list(set(all_keywords))
       vocab_size = len(keyword_vocab)
       keyword_to_index = {keyword: i for i, keyword in enumerate(keyword_vocab)}

       print(f"   🏷️ Total de palavras-chave únicas: {vocab_size}")
       print(f"   📄 Artigos com palavras-chave: {len(paper_keywords)}")

       # Construir matriz de coocorrência
       cooccurrence_matrix = np.zeros((vocab_size, vocab_size), dtype=int)

       # Preencher matriz de coocorrência
       for keywords_list in paper_keywords:
           # Obter índices das palavras-chave neste artigo
           indices = [keyword_to_index[keyword] for keyword in keywords_list if keyword in keyword_to_index]
           # Incrementar contagem para cada par de palavras-chave
           for i in range(len(indices)):
               for j in range(i+1, len(indices)):
                   idx1, idx2 = indices[i], indices[j]
                   cooccurrence_matrix[idx1, idx2] += 1
                   cooccurrence_matrix[idx2, idx1] += 1  # Matriz simétrica

       # Identificar pares de palavras-chave mais coocorrentes
       keyword_pairs = []
       for i in range(vocab_size):
           for j in range(i+1, vocab_size):
               if cooccurrence_matrix[i, j] > 0:
                   keyword_pairs.append((keyword_vocab[i], keyword_vocab[j], cooccurrence_matrix[i, j]))

       # Ordenar pares por frequência de coocorrência
       keyword_pairs.sort(key=lambda x: x[2], reverse=True)

       # Mostrar top 20 pares de coocorrência
       print("   🔗 Top 20 pares de palavras-chave coocorrentes:")
       for i, (kw1, kw2, count) in enumerate(keyword_pairs[:20], 1):
           print(f"      {i:2d}. '{kw1}' + '{kw2}': {count} coocorrências")

       # Encontrar palavras-chave mais centrais (alto grau de coocorrência)
       keyword_degrees = np.sum(cooccurrence_matrix > 0, axis=1)  # Número de outras palavras-chave com que cada uma coocorre
       keyword_total_cooc = np.sum(cooccurrence_matrix, axis=1)   # Total de coocorrências

       # Criar dataframe para análise
       keyword_stats = pd.DataFrame({
           'keyword': keyword_vocab,
           'degree': keyword_degrees,
           'total_cooccurrences': keyword_total_cooc
       }).sort_values('degree', ascending=False)

       print("   🌟 Top 15 palavras-chave mais conectadas:")
       for i, (_, row) in enumerate(keyword_stats.head(15).iterrows(), 1):
           print(f"      {i:2d}. {row['keyword']:<25} Conecta com {row['degree']:>2d} outras ({row['total_cooccurrences']:>3d} coocorrências)")

       # Gráfico de rede de coocorrência (para top palavras-chave)
       if len(keyword_pairs) > 0:
           # Criar grafo
           G = nx.Graph()

           # Adicionar nós (top 30 palavras-chave por grau)
           top_keywords_for_network = keyword_stats.head(30)
           for _, row in top_keywords_for_network.iterrows():
               G.add_node(row['keyword'], degree=row['degree'], total_cooc=row['total_cooccurrences'])

           # Adicionar arestas (pares com alta coocorrência)
           top_pairs_for_network = keyword_pairs[:50]  # Top 50 pares
           for kw1, kw2, count in top_pairs_for_network:
               if kw1 in G.nodes() and kw2 in G.nodes():
                   G.add_edge(kw1, kw2, weight=count)

           # Visualizar rede se houver nós e arestas
           if len(G.nodes()) > 2 and len(G.edges()) > 0:
               plt.figure(figsize=(16, 12))

               # Layout
               pos = nx.spring_layout(G, k=2, iterations=50)

               # Tamanhos dos nós baseados no grau
               node_sizes = [G.nodes[node]['degree'] * 100 for node in G.nodes()]

               # Cores dos nós baseadas no total de coocorrências
               node_colors = [G.nodes[node]['total_cooc'] for node in G.nodes()]

               # Desenhar nós
               nodes = nx.draw_networkx_nodes(G, pos, node_size=node_sizes,
                                             node_color=node_colors, cmap=plt.cm.viridis,
                                             alpha=0.7)

               # Desenhar arestas
               edge_widths = [G.edges[edge]['weight'] / 2 for edge in G.edges()]
               nx.draw_networkx_edges(G, pos, width=edge_widths, alpha=0.5, edge_color='gray')

               # Desenhar labels
               nx.draw_networkx_labels(G, pos, font_size=10, font_weight='bold')

               plt.title('Rede de Coocorrência de Palavras-Chave\n(Tamanho do nó = conectividade, Cor = total de coocorrências)')
               plt.axis('off')
               plt.colorbar(nodes, ax=plt.gca(), label='Total de Coocorrências')

               plt.tight_layout()
               plt.savefig(self.output_dir / 'keyword_cooccurrence_network.png', dpi=300, bbox_inches='tight')
               plt.show()

               # Estatísticas da rede
               print(f"   📊 Estatísticas da rede de coocorrência:")
               print(f"      Nós (palavras-chave): {G.number_of_nodes()}")
               print(f"      Arestas (coocorrências): {G.number_of_edges()}")
               print(f"      Densidade da rede: {nx.density(G):.4f}")
               if len(G.nodes()) > 1:
                   print(f"      Centralidade média: {np.mean(list(nx.degree_centrality(G).values())):.4f}")

       # Matriz de coocorrência (heatmap para top 20 palavras-chave)
       if vocab_size > 1:
           # Selecionar top 10 palavras-chave por grau
           top_keywords_matrix = keyword_stats.head(10)
           top_indices = [keyword_to_index[kw] for kw in top_keywords_matrix['keyword']]

           # Criar submatriz
           cooc_matrix_subset = cooccurrence_matrix[np.ix_(top_indices, top_indices)]

           # Criar heatmap
           plt.figure(figsize=(12, 10))
           sns.heatmap(cooc_matrix_subset,
                      xticklabels=[kw[:15] for kw in top_keywords_matrix['keyword']],
                      yticklabels=[kw[:15] for kw in top_keywords_matrix['keyword']],
                      annot=True, fmt='d', cmap='YlOrRd', cbar=True)
           plt.title('Matriz de Coocorrência - Top 10 Palavras-Chave')
           plt.xlabel('Palavras-Chave')
           plt.ylabel('Palavras-Chave')
           plt.xticks(rotation=45, ha='right')
           plt.yticks(rotation=0)
           plt.tight_layout()
           plt.savefig(self.output_dir / 'keyword_cooccurrence_heatmap.png', dpi=300, bbox_inches='tight')
           plt.show()

       # Verificar se há dados de periódicos
       if 'journal' not in self.df.columns:
           print("   ⚠️ Dados de periódicos não disponíveis")
           return

       # Contar publicações por periódico
       journal_counts = self.df['journal'].value_counts()

       if len(journal_counts) == 0:
           print("   ⚠️ Dados insuficientes para análise de concentração")
           return

       # Ordenar periódicos por número de publicações
       sorted_journals = journal_counts.sort_values(ascending=False)
       total_papers = len(self.df)
       cumulative_papers = 0
       bradford_zones = []
       zone_number = 1
       papers_in_zone = 0

       # Lei de Bradford: dividir periódicos em zonas onde cada zona tem aproximadamente
       # o mesmo número de artigos, mas a zona 1 tem menos periódicos que as subsequentes
       # Zona 1: Periódicos de alta produtividade
       # Zona 2: Mais periódicos que a zona 1, mas mesmo número de artigos
       # Zona 3: Mais periódicos que a zona 2, mas mesmo número de artigos

       papers_per_zone = total_papers / 3  # Idealmente dividir em 3 zonas com mesmo número de artigos

       print(f"   📊 Total de artigos: {total_papers}")
       print(f"   📰 Total de periódicos: {len(sorted_journals)}")
       print(f"   📊 Artigos por zona de Bradford (ideal): {papers_per_zone:.0f}")

       # Classificar periódicos em zonas de Bradford
       current_zone_papers = 0
       zone_journals = []
       zone_info = []

       for journal, count in sorted_journals.items():
           cumulative_papers += count
           current_zone_papers += count
           papers_in_zone += count
           zone_journals.append(journal)

           # Verificar se completou uma zona (aproximadamente 1/3 dos artigos)
           if current_zone_papers >= papers_per_zone * 0.9 or journal == sorted_journals.index[-1]:
               zone_info.append({
                   'zone': zone_number,
                   'journals': zone_journals.copy(),
                   'journal_count': len(zone_journals),
                   'paper_count': current_zone_papers,
                   'cumulative_papers': cumulative_papers,
                   'percentage': (cumulative_papers / total_papers) * 100
               })

               # Imprimir informações da zona
               print(f"   📚 Zona {zone_number}: {len(zone_journals)} periódicos, {current_zone_papers} artigos")
               if zone_number <= 3:
                   print(f"      Principais periódicos:")
                   for i, journ in enumerate(zone_journals[:5], 1):
                       journ_count = journal_counts[journ]
                       print(f"         {i}. {journ[:30]:<30} ({journ_count} artigos)")

               # Preparar para próxima zona
               zone_number += 1
               current_zone_papers = 0
               zone_journals = []

               # Limitar a 5 zonas para evitar excesso de detalhes
               if zone_number > 5:
                   break

       # Se ainda houver periódicos não alocados, colocar na última zona
       if zone_journals and zone_number <= 5:
           zone_info.append({
               'zone': zone_number,
               'journals': zone_journals.copy(),
               'journal_count': len(zone_journals),
               'paper_count': current_zone_papers,
               'cumulative_papers': cumulative_papers,
               'percentage': (cumulative_papers / total_papers) * 100
           })
           print(f"   📚 Zona {zone_number}: {len(zone_journals)} periódicos, {current_zone_papers} artigos")

       # Calcular coeficiente de concentração de Bradford
       # Verificar relação entre número de periódicos e artigos por zona
       if len(zone_info) >= 2:
           # Comparar zona 1 com zona 2
           zone1_journals = zone_info[0]['journal_count']
           zone2_journals = zone_info[1]['journal_count'] if len(zone_info) > 1 else 0

           if zone1_journals > 0 and zone2_journals > 0:
               journal_ratio = zone2_journals / zone1_journals
               print(f"   📊 Razão de periódicos (Zona 2/Zona 1): {journal_ratio:.1f}:1")
               print(f"   🔍 Conformidade com Lei de Bradford: {journal_ratio:.1f}x periódicos na Zona 2")

       # Gráfico da distribuição de Bradford
       zones = [info['zone'] for info in zone_info]
       journal_counts_zones = [info['journal_count'] for info in zone_info]
       paper_counts_zones = [info['paper_count'] for info in zone_info]

       fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

       # Gráfico de barras para periódicos por zona
       bars1 = ax1.bar(zones, journal_counts_zones, color='skyblue', alpha=0.7, edgecolor='black')
       ax1.set_xlabel('Zonas de Bradford')
       ax1.set_ylabel('Número de Periódicos')
       ax1.set_title('Distribuição de Periódicos por Zona de Bradford')
       ax1.grid(True, alpha=0.3)

       # Adicionar valores nas barras
       for bar, count in zip(bars1, journal_counts_zones):
           ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                   str(count), ha='center', va='bottom')

       # Gráfico de barras para artigos por zona
       bars2 = ax2.bar(zones, paper_counts_zones, color='lightcoral', alpha=0.7, edgecolor='black')
       ax2.set_xlabel('Zonas de Bradford')
       ax2.set_ylabel('Número de Artigos')
       ax2.set_title('Distribuição de Artigos por Zona de Bradford')
       ax2.grid(True, alpha=0.3)

       # Adicionar valores nas barras
       for bar, count in zip(bars2, paper_counts_zones):
           ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                   str(int(count)), ha='center', va='bottom')

       plt.tight_layout()
       plt.savefig(self.output_dir / 'bradford_concentration_analysis.png', dpi=300, bbox_inches='tight')
       plt.show()

       # Calcular índice de Gini para concentração de periódicos
       # Converter para array numpy
       journal_counts_array = np.array(sorted_journals.values)
       n = len(journal_counts_array)

       if n > 1:
           # Calcular índice de Gini
           index = np.arange(1, n + 1)
           gini_numerator = np.sum((2 * index - n - 1) * journal_counts_array)
           gini_denominator = n * np.sum(journal_counts_array)
           gini_coefficient = gini_numerator / gini_denominator

           print(f"   📊 Índice de Gini de concentração: {gini_coefficient:.3f}")
           if gini_coefficient > 0.6:
               print("   ⚠️ Alta concentração de publicações em poucos periódicos")
           elif gini_coefficient > 0.4:
               print("   📊 Concentração moderada de publicações")
           else:
               print("   ✅ Distribuição relativamente equitativa de publicações")

       # Verificar se há dados de ano e citações
       if 'year' not in self.df.columns or 'citations' not in self.df.columns:
           print("   ⚠️ Dados de ano ou citações não disponíveis")
           return

       current_year = datetime.now().year
       df_with_data = self.df.dropna(subset=['year', 'citations'])

       if len(df_with_data) == 0:
           print("   ⚠️ Dados insuficientes para análise de obsolescência")
           return

       # Calcular idade dos artigos
       df_with_data = df_with_data.copy()
       df_with_data['age'] = current_year - df_with_data['year']

       # Filtrar artigos com idade entre 0 e 20 anos (janela relevante)
       df_recent = df_with_data[(df_with_data['age'] >= 0) & (df_with_data['age'] <= 20)]

       if len(df_recent) == 0:
           print("   ⚠️ Dados insuficientes para análise de obsolescência")
           return

       # Calcular Índice de Price
       # Artigos considerados "atuais" (<=5 anos)
       current_papers = df_recent[df_recent['age'] <= 5]
       total_papers = len(df_recent)
       total_citations = df_recent['citations'].sum()
       current_citations = current_papers['citations'].sum()

       # Índice de Price: proporção de artigos/citações recentes
       price_paper_index = (len(current_papers) / total_papers) * 100 if total_papers > 0 else 0
       price_citation_index = (current_citations / total_citations) * 100 if total_citations > 0 else 0

       print(f"   📊 Período analisado: {int(df_recent['year'].min())} - {int(df_recent['year'].max())}")
       print(f"   📄 Total de artigos analisados: {total_papers}")
       print(f"   📚 Total de citações: {int(total_citations)}")
       print(f"   📊 Índice de Price (artigos): {price_paper_index:.1f}%")
       print(f"   📊 Índice de Price (citações): {price_citation_index:.1f}%")

       # Distribuição por idade
       age_groups = df_recent.groupby('age').agg({
           'title': 'count',
           'citations': 'sum'
       }).rename(columns={'title': 'papers', 'citations': 'citations'})

       # Calcular porcentagens
       age_groups['paper_percentage'] = (age_groups['papers'] / age_groups['papers'].sum()) * 100
       age_groups['citation_percentage'] = (age_groups['citations'] / age_groups['citations'].sum()) * 100

       print("   📊 Distribuição por idade:")
       for age in sorted(age_groups.index):
           papers = age_groups.loc[age, 'papers']
           citations = age_groups.loc[age, 'citations']
           paper_pct = age_groups.loc[age, 'paper_percentage']
           citation_pct = age_groups.loc[age, 'citation_percentage']
           print(f"      {age:2d} anos: {papers:>3d} artigos ({paper_pct:>5.1f}%) | {int(citations):>4d} citações ({citation_pct:>5.1f}%)")

       # Gráfico de distribuição por idade
       ages = sorted(age_groups.index)
       paper_counts = [age_groups.loc[age, 'papers'] for age in ages]
       citation_counts = [age_groups.loc[age, 'citations'] for age in ages]

       fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

       # Gráfico de barras para artigos
       bars1 = ax1.bar(ages, paper_counts, color='skyblue', alpha=0.7, edgecolor='black')
       ax1.set_xlabel('Idade dos Artigos (anos)')
       ax1.set_ylabel('Número de Artigos')
       ax1.set_title('Distribuição de Artigos por Idade')
       ax1.grid(True, alpha=0.3)

       # Adicionar valores nas barras
       for bar, count in zip(bars1, paper_counts):
           ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                   str(count), ha='center', va='bottom')

       # Gráfico de barras para citações
       bars2 = ax2.bar(ages, citation_counts, color='lightcoral', alpha=0.7, edgecolor='black')
       ax2.set_xlabel('Idade dos Artigos (anos)')
       ax2.set_ylabel('Número de Citações')
       ax2.set_title('Distribuição de Citações por Idade')
       ax2.grid(True, alpha=0.3)

       # Adicionar valores nas barras
       for bar, count in zip(bars2, citation_counts):
           ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                   str(int(count)), ha='center', va='bottom')

       plt.tight_layout()
       plt.savefig(self.output_dir / 'literature_obsolescence_analysis.png', dpi=300, bbox_inches='tight')
       plt.show()

       # Calcular meia-vida da literatura
       # Idade em que metade das citações foi acumulada
       cumulative_citations = age_groups['citations'].sort_index(ascending=False).cumsum()
       half_total_citations = total_citations / 2

       half_life = None
       for age in sorted(cumulative_citations.index, reverse=True):
           if cumulative_citations[age] >= half_total_citations:
               half_life = age
               break

       if half_life is not None:
           print(f"   ⏳ Meia-vida da literatura: {half_life} anos")
       else:
           print("   ⏳ Não foi possível calcular a meia-vida da literatura")

       # Verificar se há dados de palavras-chave
       if 'keywords' not in self.df.columns:
           print("   ⚠️ Dados de palavras-chave não disponíveis")
           return

       # Processar palavras-chave
       all_keywords = []
       paper_keywords = []  # Lista de palavras-chave por artigo

       for keywords_row in self.df['keywords'].dropna():
           if str(keywords_row).lower() not in ['nan', 'none', '']:
               individual_keywords = re.split(r'[;,]', str(keywords_row))
               clean_keywords = [k.strip().lower().title() for k in individual_keywords
                               if len(k.strip()) > 3 and not k.strip().isdigit()]
               if clean_keywords:
                   all_keywords.extend(clean_keywords)
                   paper_keywords.append(clean_keywords)

       if not all_keywords:
           print("   ⚠️ Dados insuficientes para análise temática")
           return

       # Calcular frequência de palavras-chave
       keyword_counts = Counter(all_keywords)
       total_keywords = len(all_keywords)

       # Calcular Índice de Shannon
       # H = -Σ(p_i * log(p_i)) onde p_i é a proporção da palavra-chave i
       shannon_index = 0
       for count in keyword_counts.values():
           p_i = count / total_keywords
           if p_i > 0:
               shannon_index -= p_i * np.log(p_i)

       # Normalizar o índice de Shannon (dividir por log(total de categorias))
       max_shannon = np.log(len(keyword_counts)) if len(keyword_counts) > 1 else 1
       normalized_shannon = shannon_index / max_shannon if max_shannon > 0 else 0

       print(f"   📊 Índice de Shannon: {shannon_index:.3f}")
       print(f"   📊 Índice de Shannon normalizado: {normalized_shannon:.3f}")
       print(f"   🏷️ Total de palavras-chave únicas: {len(keyword_counts)}")
       print(f"   📄 Total de ocorrências de palavras-chave: {total_keywords}")

       # Palavras-chave mais comuns
       top_keywords = keyword_counts.most_common(15)
       print("   🔤 Top 15 palavras-chave mais frequentes:")
       for i, (keyword, count) in enumerate(top_keywords, 1):
           percentage = (count / total_keywords) * 100
           print(f"      {i:2d}. {keyword:<25} {count:>4} ocorrências ({percentage:.1f}%)")

       # Calcular diversidade temática por ano
       yearly_diversity = {}
       yearly_data = {}

       # Associar palavras-chave com anos
       for _, row in self.df.iterrows():
           year = row['year']
           keywords_str = str(row['keywords'])
           if pd.notna(year) and keywords_str.lower() not in ['nan', 'none', '']:
               individual_keywords = re.split(r'[;,]', keywords_str)
               clean_keywords = [k.strip().lower().title() for k in individual_keywords
                               if len(k.strip()) > 3 and not k.strip().isdigit()]
               if clean_keywords:
                   if year not in yearly_data:
                       yearly_data[year] = []
                   yearly_data[year].extend(clean_keywords)

       # Calcular índice de Shannon para cada ano
       for year, keywords in yearly_data.items():
           if keywords:
               year_keyword_counts = Counter(keywords)
               total_year_keywords = len(keywords)
               year_shannon = 0
               for count in year_keyword_counts.values():
                   p_i = count / total_year_keywords
                   if p_i > 0:
                       year_shannon -= p_i * np.log(p_i)
               yearly_diversity[year] = year_shannon

       # Gráfico de evolução da diversidade temática
       if yearly_diversity:
           years = sorted(yearly_diversity.keys())
           diversity_values = [yearly_diversity[year] for year in years]

           plt.figure(figsize=(12, 6))
           plt.plot(years, diversity_values, marker='o', linewidth=2, markersize=8, color='blue')
           plt.xlabel('Ano')
           plt.ylabel('Índice de Shannon')
           plt.title('Evolução da Diversidade Temática (Índice de Shannon)')
           plt.grid(True, alpha=0.3)

           # Adicionar valores nos pontos
           for year, value in zip(years, diversity_values):
               plt.annotate(f'{value:.2f}', (year, value), textcoords="offset points",
                           xytext=(0,10), ha='center')

           plt.tight_layout()
           plt.savefig(self.output_dir / 'thematic_diversity_analysis.png', dpi=300, bbox_inches='tight')
           plt.show()

       # Gráfico das palavras-chave mais frequentes
       if top_keywords:
           keywords_names = [keyword for keyword, _ in top_keywords]
           keyword_counts_values = [count for _, count in top_keywords]

           plt.figure(figsize=(12, 8))
           bars = plt.barh(range(len(keywords_names)), keyword_counts_values, color='lightgreen', alpha=0.7)
           plt.yticks(range(len(keywords_names)), keywords_names)
           plt.xlabel('Número de Ocorrências')
           plt.title('Top 15 Palavras-Chave Mais Frequentes')
           plt.gca().invert_yaxis()  # Para mostrar a mais frequente no topo

           # Adicionar valores nas barras
           for i, (bar, count) in enumerate(zip(bars, keyword_counts_values)):
               plt.text(bar.get_width() + 0.5, bar.get_y() + bar.get_height()/2,
                       str(count), ha='left', va='center')

           plt.tight_layout()
           plt.savefig(self.output_dir / 'top_keywords_diversity.png', dpi=300, bbox_inches='tight')
           plt.show()

       # Verificar se há dados de afiliação de países
       if 'country_affiliation' not in self.df.columns:
           print("   ⚠️ Dados de afiliação de países não disponíveis")
           return

       # Contadores
       international_collaborations = 0
       domestic_collaborations = 0
       single_country_papers = 0
       total_papers_with_countries = 0

       # Análise por país
       country_publications = defaultdict(int)
       country_collaborations = defaultdict(int)
       collaboration_pairs = defaultdict(int)

       for _, row in self.df.iterrows():
           countries_str = str(row['country_affiliation'])
           if countries_str and countries_str not in ['nan', '']:
               countries = [c.strip() for c in countries_str.split(';') if c.strip()]
               if countries:
                   total_papers_with_countries += 1

                   # Contar publicações por país
                   for country in countries:
                       country_publications[country] += 1

                   # Analisar tipo de colaboração
                   unique_countries = list(set(countries))
                   if len(unique_countries) == 1:
                       single_country_papers += 1
                   else:
                       international_collaborations += 1
                       # Contar colaborações entre pares de países
                       for i in range(len(unique_countries)):
                           for j in range(i+1, len(unique_countries)):
                               pair = tuple(sorted([unique_countries[i], unique_countries[j]]))
                               collaboration_pairs[pair] += 1

                       # Contar colaborações por país
                       for country in unique_countries:
                           country_collaborations[country] += 1

       if total_papers_with_countries == 0:
           print("   ⚠️ Dados insuficientes para análise de colaboração internacional")
           return

       # Calcular percentuais
       international_rate = (international_collaborations / total_papers_with_countries) * 100
       single_country_rate = (single_country_papers / total_papers_with_countries) * 100

       print(f"   📊 Colaboração internacional: {international_rate:.1f}% dos artigos")
       print(f"   📊 Artigos de país único: {single_country_rate:.1f}% dos artigos")
       print(f"   📈 Total de artigos com dados de país: {total_papers_with_countries}")

       # Países mais produtivos
       top_countries = sorted(country_publications.items(), key=lambda x: x[1], reverse=True)[:10]
       print("   🌍 Top 10 países por produção:")
       for i, (country, count) in enumerate(top_countries, 1):
           collaboration_ratio = (country_collaborations[country] / count) * 100 if count > 0 else 0
           print(f"      {i:2d}. {country:<20} {count:>3} artigos ({collaboration_ratio:.1f}% colaboração)")

       # Pares de países mais colaborativos
       top_collaborations = sorted(collaboration_pairs.items(), key=lambda x: x[1], reverse=True)[:10]
       print("   🤝 Top 10 colaborações entre países:")
       for i, (pair, count) in enumerate(top_collaborations, 1):
           print(f"      {i:2d}. {pair[0]} - {pair[1]}: {count} artigos")

       # Gráfico de países mais produtivos
       if top_countries:
           countries_names = [country[:15] for country, _ in top_countries]
           publication_counts = [count for _, count in top_countries]
           collaboration_rates = [(country_collaborations[country] / country_publications[country]) * 100
                                 if country_publications[country] > 0 else 0
                                 for country, _ in top_countries]

           fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

           # Gráfico de barras de publicações
           bars1 = ax1.bar(range(len(countries_names)), publication_counts, color='skyblue', alpha=0.7)
           ax1.set_xlabel('Países')
           ax1.set_ylabel('Número de Publicações')
           ax1.set_title('Publicações por País')
           ax1.set_xticks(range(len(countries_names)))
           ax1.set_xticklabels(countries_names, rotation=45, ha='right')

           # Adicionar valores nas barras
           for bar, count in zip(bars1, publication_counts):
               ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                       str(count), ha='center', va='bottom')

           # Gráfico de taxa de colaboração
           bars2 = ax2.bar(range(len(countries_names)), collaboration_rates, color='lightcoral', alpha=0.7)
           ax2.set_xlabel('Países')
           ax2.set_ylabel('Taxa de Colaboração (%)')
           ax2.set_title('Taxa de Colaboração por País')
           ax2.set_xticks(range(len(countries_names)))
           ax2.set_xticklabels(countries_names, rotation=45, ha='right')
           ax2.set_ylim(0, 100)

           # Adicionar valores nas barras
           for bar, rate in zip(bars2, collaboration_rates):
               ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                       f'{rate:.1f}%', ha='center', va='bottom')

           plt.tight_layout()
           plt.savefig(self.output_dir / 'international_collaboration_analysis.png', dpi=300, bbox_inches='tight')
           plt.show()

       # Verificar se há dados de citações
       if 'citations' not in self.df.columns or self.df['citations'].sum() == 0:
           print("   ⚠️ Dados de citação não disponíveis")
           return

       # Calcular H-index para todos os artigos
       citations_sorted = sorted(self.df['citations'], reverse=True)
       h_index = 0
       for i, citations in enumerate(citations_sorted):
           if citations >= i + 1:
               h_index = i + 1
           else:
               break

       print(f"   📚 H-index geral: {h_index}")

       # Calcular H-index por autor
       author_citations = defaultdict(list)
       for _, row in self.df.iterrows():
           authors = str(row['authors']).split(';')
           citations = row['citations']
           for author in authors:
               author = author.strip()
               if len(author) > 2:
                   author_citations[author].append(citations)

       # Calcular H-index para cada autor
       author_h_index = {}
       for author, citations_list in author_citations.items():
           citations_sorted = sorted(citations_list, reverse=True)
           h_idx = 0
           for i, citations in enumerate(citations_sorted):
               if citations >= i + 1:
                   h_idx = i + 1
               else:
                   break
           author_h_index[author] = h_idx

       # Ordenar autores por H-index
       sorted_authors = sorted(author_h_index.items(), key=lambda x: x[1], reverse=True)

       # Mostrar top 15 autores por H-index
       print("   👥 Top 15 autores por H-index:")
       for i, (author, h_idx) in enumerate(sorted_authors[:15], 1):
           print(f"      {i:2d}. {author[:30]:<30} H-index: {h_idx}")

       # Gráfico de H-index dos top autores
       if sorted_authors:
           top_authors_data = sorted_authors[:15]
           authors_names = [author[:20] for author, _ in top_authors_data]
           h_indices = [h_idx for _, h_idx in top_authors_data]

           plt.figure(figsize=(12, 8))
           bars = plt.barh(range(len(authors_names)), h_indices, color='skyblue', alpha=0.7)
           plt.yticks(range(len(authors_names)), authors_names)
           plt.xlabel('H-index')
           plt.title('Top 15 Autores por H-index')
           plt.gca().invert_yaxis()  # Para mostrar o maior no topo

           # Adicionar valores nas barras
           for i, (bar, h_idx) in enumerate(zip(bars, h_indices)):
               plt.text(bar.get_width() + 0.1, bar.get_y() + bar.get_height()/2,
                       str(h_idx), ha='left', va='center')

           plt.tight_layout()
           plt.savefig(self.output_dir / 'h_index_analysis.png', dpi=300, bbox_inches='tight')
           plt.show()

       # Estatísticas gerais
       h_indices_all = list(author_h_index.values())
       if h_indices_all:
           print(f"   📊 Estatísticas de H-index:")
           print(f"      Média: {np.mean(h_indices_all):.2f}")
           print(f"      Mediana: {np.median(h_indices_all):.2f}")
           print(f"      Desvio padrão: {np.std(h_indices_all):.2f}")
           print(f"      Máximo: {np.max(h_indices_all)}")

       # Obter dados de produção por ano
       yearly_production = self.df['year'].value_counts().sort_index()

       if len(yearly_production) < 3:
           print("   ⚠️ Dados insuficientes para análise de crescimento")
           return

       # Preparar dados para regressão
       years = np.array(yearly_production.index).reshape(-1, 1)
       publications = np.array(yearly_production.values)

       # Ajuste exponencial: y = a * e^(b*x)
       # Linearizar: ln(y) = ln(a) + b*x
       log_publications = np.log(publications)

       # Regressão linear nos dados linearizados
       from sklearn.linear_model import LinearRegression
       model = LinearRegression()
       model.fit(years, log_publications)

       # Parâmetros do modelo exponencial
       a = np.exp(model.intercept_)
       b = model.coef_[0]

       # Previsões
       future_years = np.arange(years.min(), years.max() + 6).reshape(-1, 1)  # 5 anos à frente
       predicted_log = model.predict(future_years)
       predicted_publications = np.exp(predicted_log)

       # Calcular R²
       r2_score = model.score(years, log_publications)

       print(f"   📊 Modelo de crescimento exponencial: y = {a:.2f} * e^({b:.3f}*x)")
       print(f"   🔍 Coeficiente de determinação (R²): {r2_score:.3f}")

       # Gráfico
       plt.figure(figsize=(12, 6))
       plt.scatter(years, publications, color='blue', s=100, label='Dados reais', zorder=3)
       plt.plot(future_years, predicted_publications, '--', color='red', linewidth=2,
               label=f'Modelo preditivo (R² = {r2_score:.3f})')

       # Preencher área de previsão
       split_index = len(years)
       plt.fill_between(future_years.flatten()[split_index-1:],
                       predicted_publications[split_index-1:],
                       alpha=0.3, color='red', label='Previsão')

       plt.xlabel('Ano')
       plt.ylabel('Número de Publicações')
       plt.title('Crescimento Exponencial e Previsão de Publicações')
       plt.legend()
       plt.grid(True, alpha=0.3)
       plt.tight_layout()
       plt.savefig(self.output_dir / 'exponential_growth_analysis.png', dpi=300, bbox_inches='tight')
       plt.show()

       # Calcular taxa de crescimento anual
       growth_rate = (np.exp(b) - 1) * 100
       print(f"   📈 Taxa de crescimento anual: {growth_rate:.2f}%")

       # Previsões para próximos anos
       print("   🔮 Previsões para próximos anos:")
       for i in range(len(years), len(future_years)):
           year = int(future_years[i][0])
           pred = int(predicted_publications[i])
           print(f"      {year}: ~{pred} publicações")

       # Contar número de publicações por autor
       author_publications = defaultdict(int)
       for authors_row in self.df['authors'].dropna():
           authors_list = [a.strip() for a in str(authors_row).split(';')
                          if len(a.strip()) > 2]
           for author in authors_list:
               author_publications[author] += 1

       if not author_publications:
           print("   ⚠️ Dados insuficientes para análise de Lotka")
           return

       # Contar número de autores por número de publicações
       productivity_distribution = defaultdict(int)
       for author, count in author_publications.items():
           productivity_distribution[count] += 1

       # Ordenar por número de publicações
       sorted_distribution = sorted(productivity_distribution.items())

       # Preparar dados para gráfico
       publications_counts = [item[0] for item in sorted_distribution]
       authors_counts = [item[1] for item in sorted_distribution]

       # Gráfico da distribuição
       plt.figure(figsize=(12, 6))
       plt.bar(publications_counts, authors_counts, color='skyblue', alpha=0.7, edgecolor='black')
       plt.xlabel('Número de Publicações por Autor')
       plt.ylabel('Número de Autores')
       plt.title('Distribuição de Produtividade dos Autores (Lei de Lotka)')
       plt.yscale('log')
       plt.xscale('log')

       # Adicionar valores nas barras
       for i, (x, y) in enumerate(zip(publications_counts, authors_counts)):
           plt.text(x, y, str(y), ha='center', va='bottom')

       plt.tight_layout()
       plt.savefig(self.output_dir / 'lotka_law_analysis.png', dpi=300, bbox_inches='tight')
       plt.show()

       # Calcular ajuste para lei de Lotka (inverso do quadrado)
       if len(sorted_distribution) > 1:
           # Primeiro ponto como referência
           p1, a1 = sorted_distribution[0]
           expected_values = [a1 / (p ** 2) for p, _ in sorted_distribution]

           # Calcular coeficiente de determinação R²
           actual_values = [a for _, a in sorted_distribution]
           correlation_matrix = np.corrcoef(actual_values, expected_values)
           r_squared = correlation_matrix[0, 1] ** 2

           print(f"   🔍 Ajuste à Lei de Lotka (R²): {r_squared:.3f}")
           if r_squared > 0.7:
               print("   ✅ A distribuição segue aproximadamente a Lei de Lotka")
           else:
               print("   ⚠️ A distribuição não segue claramente a Lei de Lotka")

       print(f"   📊 Autores analisados: {len(author_publications)}")
       print(f"   📈 Publicações por autor (média): {np.mean(list(author_publications.values())):.2f}")

    def perform_cluster_analysis(self, n_clusters: int = 5) -> Optional[Tuple]:
        """Análise de clusters temáticos."""
        print("🎯 Realizando análise de clusters...")

        # Preparar documentos
        documents = []
        titles = []

        for _, row in self.df.iterrows():
            doc_text = f"{row['title']} {row['keywords']} {str(row['abstract'])[:200]}"
            documents.append(doc_text)
            titles.append(row['title'])

        if len(documents) < 10:
            print("   ⚠️ Dados insuficientes para clustering")
            return None, None

        # Vetorização TF-IDF
        vectorizer = TfidfVectorizer(
            max_features=500,
            stop_words='english',
            ngram_range=(1, 2),
            min_df=2
        )

        tfidf_matrix = vectorizer.fit_transform(documents)

        # Clustering
        n_clusters = min(n_clusters, len(documents) // 10)
        if n_clusters < 2:
            print("   ⚠️ Dados insuficientes para clustering")
            return None, None

        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        clusters = kmeans.fit_predict(tfidf_matrix)

        # Analisar clusters
        feature_names = vectorizer.get_feature_names_out()

        print(f"📊 Identificados {n_clusters} clusters temáticos:")

        for i in range(n_clusters):
            cluster_docs = [titles[j] for j, c in enumerate(clusters) if c == i]
            print(f"\n🎯 CLUSTER {i+1}:")
            print(f"   📄 {len(cluster_docs)} artigos")

            # Termos representativos
            center = kmeans.cluster_centers_[i]
            top_terms_idx = center.argsort()[-10:][::-1]
            top_terms = [feature_names[idx] for idx in top_terms_idx]
            print(f"   🏷️ Termos: {', '.join(top_terms[:5])}")

            # Exemplos
            print(f"   📚 Exemplos:")
            for doc in cluster_docs[:3]:
                print(f"      • {doc[:60]}...")

        return clusters, feature_names

    def export_results(self) -> None:
        """Exporta todos os resultados."""
        print("💾 Exportando resultados...")

        # Dataset curado
        output_file = self.output_dir / 'dataset_llm_ehr_curado.csv'
        self.df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"✅ Dataset salvo: {output_file}")

        # Estatísticas resumidas
        yearly_production = self.df['year'].value_counts().sort_index()
        all_authors = [a.strip() for authors in self.df['authors'].dropna()
                      for a in str(authors).split(';') if len(a.strip()) > 2]
        author_counts = Counter(all_authors)
        journal_counts = self.df['journal'].value_counts()

        # Palavras-chave
        all_keywords = []
        for keywords in self.df['keywords'].dropna():
            if str(keywords).lower() not in ['nan', 'none', '']:
                individual_keywords = re.split(r'[;,]', str(keywords))
                clean_keywords = [k.strip().lower().title() for k in individual_keywords
                                if len(k.strip()) > 3]
                all_keywords.extend(clean_keywords)
        keyword_counts = Counter(all_keywords)

        summary_stats = {
            'data_summary': {
                'total_articles': len(self.df),
                'period': f"{int(self.df['year'].min())}-{int(self.df['year'].max())}",
                'sources': self.df['source'].value_counts().to_dict(),
                'yearly_production': yearly_production.to_dict()
            },
            'top_authors': dict(author_counts.most_common(10)),
            'top_journals': journal_counts.head(10).to_dict(),
            'top_keywords': dict(keyword_counts.most_common(15))
        }

        stats_file = self.output_dir / 'summary_statistics.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(summary_stats, f, indent=2, ensure_ascii=False)
        print(f"✅ Estatísticas salvas: {stats_file}")

        # Dados para VOSviewer
        vos_data = self.df[['title', 'authors', 'journal', 'year', 'abstract', 'keywords']].copy()
        vos_file_csv = self.output_dir / 'data_for_vosviewer.csv'
        vos_file_json = self.output_dir / 'data_for_vosviewer.json'

        vos_data.to_csv(vos_file_csv, index=False, encoding='utf-8')
        vos_data.to_json(vos_file_json, orient='records', force_ascii=False, indent=2)

        print(f"✅ Dados VOSviewer salvos: {vos_file_csv} e {vos_file_json}")

    def generate_comprehensive_report(self) -> str:
        """Gera relatório bibliométrico completo."""
        print("📋 Gerando relatório completo...")

        # Estatísticas básicas
        total_articles = len(self.df)
        period_range = f"{int(self.df['year'].min())}-{int(self.df['year'].max())}"
        sources = ', '.join(self.df['source'].unique())

        # Autores únicos
        all_authors = [a.strip() for authors in self.df['authors'].dropna()
                      for a in str(authors).split(';') if len(a.strip()) > 2]
        unique_authors = len(set(all_authors))

        # Contadores
        author_counts = Counter(all_authors)
        journal_counts = self.df['journal'].value_counts()
        yearly_counts = self.df['year'].value_counts().sort_index()

        # Palavras-chave
        all_keywords = []
        for keywords in self.df['keywords'].dropna():
            if str(keywords).lower() not in ['nan', 'none', '']:
                individual_keywords = re.split(r'[;,]', str(keywords))
                clean_keywords = [k.strip().lower().title() for k in individual_keywords
                                if len(k.strip()) > 3]
                all_keywords.extend(clean_keywords)
        keyword_counts = Counter(all_keywords)

        report_content = f"""# RELATÓRIO BIBLIOMÉTRICO: LLMs EM SISTEMAS DE RECUPERAÇÃO DE INFORMAÇÃO EM SAÚDE

## 1. RESUMO EXECUTIVO

Este relatório apresenta uma análise bibliométrica abrangente sobre o uso de Large Language Models (LLMs)
em sistemas de recuperação de informação em registros eletrônicos de saúde, cobrindo o período de 2020 a 2024.

### Principais Números:
- **Total de artigos analisados:** {total_articles}
- **Período de cobertura:** {period_range}
- **Fontes utilizadas:** {sources}
- **Autores únicos identificados:** {unique_authors}
- **Periódicos diferentes:** {self.df['journal'].nunique()}

## 2. EVOLUÇÃO TEMPORAL

A produção científica na área apresentou o seguinte padrão temporal:

"""

        # Adicionar evolução temporal
        for year, count in yearly_counts.items():
            report_content += f"- **{int(year)}:** {count} publicações\n"

        # Calcular taxa de crescimento
        if len(yearly_counts) > 1:
            growth_rate = ((yearly_counts.iloc[-1] - yearly_counts.iloc[0]) /
                          yearly_counts.iloc[0] * 100)
            report_content += f"\n**Taxa de crescimento:** {growth_rate:.1f}% entre {int(yearly_counts.index[0])} e {int(yearly_counts.index[-1])}\n"

        report_content += f"""

## 3. PRINCIPAIS AUTORES E INSTITUIÇÕES

### Autores Mais Produtivos:
"""

        # Top 10 autores
        for i, (author, count) in enumerate(author_counts.most_common(10), 1):
            report_content += f"{i}. **{author}** - {count} publicações\n"

        report_content += f"""

## 4. PERIÓDICOS DE DESTAQUE

### Revistas com Maior Concentração de Artigos:
"""

        # Top 10 periódicos
        for i, (journal, count) in enumerate(journal_counts.head(10).items(), 1):
            report_content += f"{i}. **{journal}** - {count} artigos\n"

        report_content += f"""

## 5. ANÁLISE TEMÁTICA

### Palavras-chave Mais Frequentes:
"""

        # Top 15 palavras-chave
        for i, (keyword, count) in enumerate(keyword_counts.most_common(15), 1):
            report_content += f"{i}. **{keyword}** - {count} ocorrências\n"

        # Análise de citações se disponível
        if 'citations' in self.df.columns and self.df['citations'].sum() > 0:
            df_citations = self.df[self.df['citations'] > 0]
            total_citations = int(self.df['citations'].sum())
            avg_citations = df_citations['citations'].mean()
            most_cited = self.df.loc[self.df['citations'].idxmax()]

            report_content += f"""

## 6. IMPACTO E CITAÇÕES

- **Total de citações coletadas:** {total_citations}
- **Média de citações por artigo:** {avg_citations:.2f}
- **Artigo mais citado:** {most_cited['title']} ({int(most_cited['citations'])} citações)

### Top 5 Artigos Mais Citados:
"""

            for i, (_, row) in enumerate(self.df.nlargest(5, 'citations').iterrows(), 1):
                report_content += f"{i}. **{row['title'][:80]}...** ({int(row['citations'])} citações) - {row['year']}\n"
        else:
            report_content += f"""

## 6. IMPACTO E CITAÇÕES

- Dados de citação não disponíveis para análise de impacto.
"""

        report_content += f"""

## 7. TENDÊNCIAS E GAPS IDENTIFICADOS

### Tendências Emergentes:
1. **Crescimento exponencial** na adoção de LLMs para análise de EHR
2. **Foco crescente** em aplicações clínicas práticas
3. **Desenvolvimento** de métodos de avaliação específicos para o domínio da saúde
4. **Integração** com sistemas de apoio à decisão clínica

### Gaps de Pesquisa Identificados:
1. **Avaliação de segurança** e confiabilidade dos modelos
2. **Estudos longitudinais** de implementação em ambientes clínicos reais
3. **Análise de viés** e equidade nos modelos aplicados à saúde
4. **Padronização** de métricas de avaliação

## 8. RECOMENDAÇÕES

### Para Pesquisadores:
- Investir em estudos de validação clínica
- Desenvolver benchmarks padronizados
- Explorar colaborações interdisciplinares

### Para Instituições:
- Apoiar desenvolvimento de infraestrutura de pesquisa
- Promover parcerias internacionais
- Investir em formação de recursos humanos especializados

### Para Políticas Públicas:
- Desenvolver regulamentações específicas para IA em saúde
- Apoiar pesquisa translacional
- Promover acesso equitativo às tecnologias

## 9. CONCLUSÕES

A análise bibliométrica realizada demonstra que o campo de aplicação de LLMs em sistemas de
recuperação de informação em saúde está em rápido crescimento e evolução. Os resultados
indicam uma área de pesquisa madura o suficiente para gerar aplicações práticas, mas ainda
com significativas oportunidades de desenvolvimento.

A concentração da produção científica em determinadas regiões e instituições sugere a
necessidade de maior diversificação geográfica da pesquisa. Além disso, a identificação
de gaps específicos aponta para direções promissoras de pesquisa futura.

---

*Relatório gerado automaticamente através de análise bibliométrica sistemática.*
*Data de geração: {datetime.now().strftime('%d/%m/%Y %H:%M')}*
"""

        # Salvar relatório
        report_file = self.output_dir / 'comprehensive_bibliometric_report.md'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"✅ Relatório completo salvo: {report_file}")
        return report_content

    def run_complete_analysis(self, max_results: Optional[int] = None) -> None:
        """Executa análise bibliométrica completa."""
        print("🚀 INICIANDO ANÁLISE BIBLIOMÉTRICA COMPLETA")
        print("=" * 80)

        try:
            # 1. Coleta de dados
            self.collect_data(max_results)

            # 2. Curadoria
            self.curate_data()

            # 3. Análises
            print("\n📈 EXECUTANDO ANÁLISES")
            print("-" * 40)

            self.analyze_temporal_evolution()
            self.analyze_top_authors()
            self.analyze_journals()
            self.analyze_keywords()
            self.analyze_collaboration_network()
            self.analyze_citations()
            self.perform_cluster_analysis()

            # Novas análises bibliométricas
            self.analyze_lotka_law()
            self.analyze_exponential_growth()
            self.analyze_h_index()
            self.analyze_international_collaboration()
            self.analyze_thematic_diversity()
            self.analyze_literature_obsolescence()
            self.analyze_bradford_concentration()
            self.analyze_keyword_cooccurrence()
            self.analyze_citation_window()
            self.analyze_emerging_trends()

            # Análises adicionais solicitadas
            self.analyze_top_countries_production()  # Top 15 Países por Produção Científica
            self.analyze_brazil_specific()  # Análise especial do Brasil

            # 4. Exportação
            print("\n💾 EXPORTANDO RESULTADOS")
            print("-" * 40)

            self.export_results()
            self.generate_comprehensive_report()

            # 5. Síntese final
            self._print_final_summary()

        except Exception as e:
            print(f"❌ Erro durante a análise: {e}")
            raise

    def _print_final_summary(self) -> None:
        """Imprime síntese final da análise."""
        yearly_production = self.df['year'].value_counts().sort_index()

        print("\n" + "="*80)
        print("📋 SÍNTESE DA ANÁLISE BIBLIOMÉTRICA")
        print("="*80)

        print(f"📊 DADOS COLETADOS:")
        print(f"   • Total de artigos analisados: {len(self.df)}")
        print(f"   • Período de cobertura: {int(self.df['year'].min())} - {int(self.df['year'].max())}")
        print(f"   • Fontes utilizadas: {', '.join(self.df['source'].unique())}")

        # Principais achados
        all_authors = [a.strip() for authors in self.df['authors'].dropna()
                      for a in str(authors).split(';') if len(a.strip()) > 2]
        author_counts = Counter(all_authors)
        journal_counts = self.df['journal'].value_counts()

        print(f"\n🔍 PRINCIPAIS ACHADOS:")
        if author_counts:
            top_author = author_counts.most_common(1)[0]
            print(f"   • Autor mais produtivo: {top_author[0]} ({top_author[1]} publicações)")
        print(f"   • Revista com maior concentração: {journal_counts.index[0]} ({journal_counts.iloc[0]} artigos)")

        # Adicionar informações das novas análises
        print(f"   • H-index geral: calculado por análise de impacto")
        print(f"   • Tendências emergentes: identificadas por análise de N-grams")

        print(f"\n📈 TENDÊNCIAS IDENTIFICADAS:")
        if len(yearly_production) >= 2:
            recent_growth = yearly_production.iloc[-2:].sum() / yearly_production.iloc[:2].sum()
            print(f"   • Crescimento nos últimos anos: {((recent_growth - 1) * 100):.1f}%")
        print(f"   • Ano de maior produção: {yearly_production.idxmax()} ({yearly_production.max()} artigos)")

        if 'citations' in self.df.columns and self.df['citations'].sum() > 0:
            print(f"   • Total de citações coletadas: {int(self.df['citations'].sum())}")
            print(f"   • Média de citações por artigo: {self.df['citations'].mean():.1f}")

        # Informações adicionais das novas análises
        print(f"   • Diversidade temática: medida pelo Índice de Shannon")
        print(f"   • Colaboração internacional: analisada por dados de afiliação")
        print(f"   • Obsolescência da literatura: medida pelo Índice de Price")

        print(f"\n📁 ARQUIVOS GERADOS:")
        print(f"   • dataset_llm_ehr_curado.csv - Dataset completo curado")
        print(f"   • summary_statistics.json - Métricas consolidadas")
        print(f"   • data_for_vosviewer.csv/.json - Dados para VOSviewer")
        print(f"   • comprehensive_bibliometric_report.md - Relatório completo")
        print(f"   • Gráficos salvos em formato PNG")
        print(f"   • Análises adicionais: Lotka, Bradford, Coocorrência, Janela de Citação, etc.")

        print("\n" + "="*80)
        print("🎉 ANÁLISE BIBLIOMÉTRICA CONCLUÍDA COM SUCESSO!")
        print("="*80)


def main():
    """Função principal para execução do script."""
    print("🚀 ANÁLISE BIBLIOMÉTRICA: LLMs EM SISTEMAS DE RECUPERAÇÃO DE INFORMAÇÃO EM SAÚDE")
    print("="*80)

    # Inicializar analisador
    analyzer = BibliometricAnalyzer(
        email="<EMAIL>",  # Substitua por seu e-mail
        output_dir="outputs"
    )

    # Executar análise completa
    # Para teste rápido, use max_results=100
    # Para análise completa, use max_results=None
    analyzer.run_complete_analysis(max_results=None)


if __name__ == "__main__":
    main()

!zip -r outputs.zip outputs/
from google.colab import files
files.download('outputs.zip')