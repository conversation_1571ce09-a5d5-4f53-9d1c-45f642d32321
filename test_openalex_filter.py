import pandas as pd

df = pd.read_csv('dataset_llm_ehr_curado.csv')
df_openalex = df[df['source'] == 'OpenAlex']
df_pubmed = df[df['source'] == 'PubMed']

print(f'Total de registros: {len(df)}')
print(f'Registros OpenAlex: {len(df_openalex)}')
print(f'Registros PubMed: {len(df_pubmed)}')
print('✅ Filtro OpenAlex funcionando corretamente')

if len(df_openalex) > 0:
    print('\n📋 Primeiras 3 colunas dos dados OpenAlex:')
    print(df_openalex[['source', 'title', 'authors']].head(3))
