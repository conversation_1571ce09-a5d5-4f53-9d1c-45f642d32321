# Análise Bibliométrica - LLMs em EHR

Este é um aplicativo Streamlit para análise bibliométrica de artigos sobre Large Language Models (LLMs) em Electronic Health Records (EHR), baseado no código `bibliometric_analysis_enhanced.py`.

## 📊 Funcionalidades

O aplicativo oferece as seguintes análises:

- **📊 Visão Geral**: Estatísticas gerais do dataset
- **📈 Evolução Temporal**: Análise da produção científica ao longo dos anos
- **👥 Autores Produtivos**: Ranking dos autores mais produtivos
- **📰 Periódicos**: Análise dos periódicos mais relevantes
- **🔤 Palavras-Chave**: Análise de palavras-chave e nuvem de palavras
- **🕸️ Rede de Coautoria**: Visualização da rede de colaboração entre autores
- **📚 Análise de Citações**: Análise dos artigos mais citados
- **📊 Lei de Lotka**: <PERSON><PERSON><PERSON><PERSON> da produtividade dos autores
- **📈 Crescimento Exponencial**: Modelos preditivos de crescimento
- **🌍 Colaboração Internacional**: Análise de colaborações entre países
- **🏷️ Diversidade Temática**: Índice de Shannon para diversidade temática

## 🚀 Como Executar

### Pré-requisitos

- Python 3.8 ou superior
- Arquivo `dataset_llm_ehr_curado.csv` no mesmo diretório

### Instalação

1. Clone ou baixe os arquivos do projeto
2. Instale as dependências:

```bash
pip install -r requirements.txt
```

### Execução

Execute o aplicativo Streamlit:

```bash
streamlit run streamlit_bibliometric_app.py
```

O aplicativo será aberto automaticamente no seu navegador em `http://localhost:8501`.

## 📁 Estrutura dos Arquivos

- `streamlit_bibliometric_app.py`: Aplicativo principal
- `dataset_llm_ehr_curado.csv`: Dataset com os artigos curados
- `requirements.txt`: Dependências do projeto
- `README.md`: Este arquivo

## 📋 Formato do Dataset

O dataset deve conter as seguintes colunas:

- `source`: Fonte do artigo (PubMed, OpenAlex, etc.)
- `title`: Título do artigo
- `authors`: Autores (separados por ';')
- `journal`: Periódico
- `year`: Ano de publicação
- `abstract`: Resumo do artigo
- `keywords`: Palavras-chave (separadas por ';' ou ',')
- `citations`: Número de citações
- `country_affiliation`: Países de afiliação (separados por ';')

## 🎯 Uso

1. Execute o aplicativo
2. Use a barra lateral para selecionar as análises desejadas
3. Visualize os resultados interativos
4. Explore os gráficos e tabelas gerados

## 📊 Tecnologias Utilizadas

- **Streamlit**: Interface web interativa
- **Pandas**: Manipulação de dados
- **Plotly**: Gráficos interativos
- **NetworkX**: Análise de redes
- **Matplotlib/Seaborn**: Visualizações estáticas
- **WordCloud**: Nuvem de palavras
- **Scikit-learn**: Análises estatísticas
