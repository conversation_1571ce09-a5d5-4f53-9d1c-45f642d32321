#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste específico para as novas análises de rede: 
- Rede de Coocorrência das Palavras-chave
- Rede de Colaboração entre Instituições
"""

import pandas as pd
import networkx as nx
from collections import Counter, defaultdict
import re

def test_keyword_cooccurrence():
    """Testa a análise de coocorrência de palavras-chave."""
    print("🔗 TESTANDO REDE DE COOCORRÊNCIA DAS PALAVRAS-CHAVE")
    print("=" * 60)
    
    try:
        # Carregar dados
        df = pd.read_csv('dataset_llm_ehr_curado.csv')
        df['year'] = pd.to_numeric(df['year'], errors='coerce')
        df = df[(df['year'] >= 2020) & (df['year'] <= 2024)]
        
        print(f"✅ Dataset carregado: {len(df)} registros")
        
        # Extrair palavras-chave
        all_keywords = []
        keyword_papers = defaultdict(list)
        
        for idx, keywords_row in df['keywords'].dropna().items():
            if str(keywords_row).lower() not in ['nan', 'none', '']:
                individual_keywords = re.split(r'[;,]', str(keywords_row))
                clean_keywords = [
                    k.strip().lower().title()
                    for k in individual_keywords
                    if len(k.strip()) > 3 and not k.strip().isdigit()
                ]
                
                for keyword in clean_keywords:
                    keyword_papers[keyword].append(idx)
                
                all_keywords.extend(clean_keywords)
        
        keyword_counts = Counter(all_keywords)
        top_keywords_list = [kw for kw, count in keyword_counts.most_common(30)]
        
        print(f"✅ Palavras-chave extraídas: {len(keyword_counts)} únicas")
        print(f"✅ Top 30 palavras-chave selecionadas para análise")
        
        # Calcular coocorrências
        min_cooccurrence = 3
        cooccurrence_matrix = defaultdict(lambda: defaultdict(int))
        
        for keyword1 in top_keywords_list:
            papers1 = set(keyword_papers[keyword1])
            for keyword2 in top_keywords_list:
                if keyword1 != keyword2:
                    papers2 = set(keyword_papers[keyword2])
                    cooccurrence = len(papers1.intersection(papers2))
                    if cooccurrence >= min_cooccurrence:
                        cooccurrence_matrix[keyword1][keyword2] = cooccurrence
        
        # Criar grafo
        edges = []
        for kw1, connections in cooccurrence_matrix.items():
            for kw2, weight in connections.items():
                edges.append((kw1, kw2, weight))
        
        if edges:
            G = nx.Graph()
            for kw1, kw2, weight in edges:
                G.add_edge(kw1, kw2, weight=weight)
            
            print(f"✅ Rede de coocorrência criada:")
            print(f"   - Nós (palavras-chave): {len(G.nodes())}")
            print(f"   - Arestas (coocorrências): {len(G.edges())}")
            print(f"   - Densidade: {nx.density(G):.3f}")
            
            # Top coocorrências
            top_cooccurrences = sorted(edges, key=lambda x: x[2], reverse=True)[:5]
            print(f"\n🔝 Top 5 coocorrências:")
            for i, (kw1, kw2, weight) in enumerate(top_cooccurrences, 1):
                print(f"   {i}. {kw1} ↔ {kw2}: {weight}")
            
            return True
        else:
            print(f"⚠️ Nenhuma coocorrência encontrada com mínimo de {min_cooccurrence}")
            return False
            
    except Exception as e:
        print(f"❌ Erro na análise de coocorrência: {e}")
        return False

def test_institution_collaboration():
    """Testa a análise de colaboração entre instituições."""
    print("\n🏛️ TESTANDO REDE DE COLABORAÇÃO ENTRE INSTITUIÇÕES")
    print("=" * 60)
    
    try:
        # Carregar dados
        df = pd.read_csv('dataset_llm_ehr_curado.csv')
        df['year'] = pd.to_numeric(df['year'], errors='coerce')
        df = df[(df['year'] >= 2020) & (df['year'] <= 2024)]
        
        if 'institutions' not in df.columns:
            print("❌ Coluna 'institutions' não encontrada")
            return False
        
        # Extrair instituições
        all_institutions = []
        institution_papers = defaultdict(list)
        
        for idx, institutions_row in df['institutions'].dropna().items():
            if str(institutions_row).lower() not in ['nan', 'none', '']:
                individual_institutions = re.split(r'[;,]', str(institutions_row))
                clean_institutions = [
                    inst.strip()
                    for inst in individual_institutions
                    if len(inst.strip()) > 5
                ]
                
                for institution in clean_institutions:
                    institution_papers[institution].append(idx)
                
                all_institutions.extend(clean_institutions)
        
        if not all_institutions:
            print("⚠️ Nenhuma instituição encontrada")
            return False
        
        institution_counts = Counter(all_institutions)
        top_institutions_list = [inst for inst, count in institution_counts.most_common(30)]
        
        print(f"✅ Instituições extraídas: {len(institution_counts)} únicas")
        print(f"✅ Top 30 instituições selecionadas para análise")
        
        # Calcular colaborações
        min_collaboration = 2
        collaboration_matrix = defaultdict(lambda: defaultdict(int))
        
        for inst1 in top_institutions_list:
            papers1 = set(institution_papers[inst1])
            for inst2 in top_institutions_list:
                if inst1 != inst2:
                    papers2 = set(institution_papers[inst2])
                    collaboration = len(papers1.intersection(papers2))
                    if collaboration >= min_collaboration:
                        collaboration_matrix[inst1][inst2] = collaboration
        
        # Criar grafo
        edges = []
        for inst1, connections in collaboration_matrix.items():
            for inst2, weight in connections.items():
                edges.append((inst1, inst2, weight))
        
        if edges:
            G = nx.Graph()
            for inst1, inst2, weight in edges:
                G.add_edge(inst1, inst2, weight=weight)
            
            print(f"✅ Rede de colaboração criada:")
            print(f"   - Nós (instituições): {len(G.nodes())}")
            print(f"   - Arestas (colaborações): {len(G.edges())}")
            print(f"   - Densidade: {nx.density(G):.3f}")
            
            # Top colaborações
            top_collaborations = sorted(edges, key=lambda x: x[2], reverse=True)[:5]
            print(f"\n🤝 Top 5 colaborações:")
            for i, (inst1, inst2, weight) in enumerate(top_collaborations, 1):
                inst1_short = inst1[:30] + "..." if len(inst1) > 30 else inst1
                inst2_short = inst2[:30] + "..." if len(inst2) > 30 else inst2
                print(f"   {i}. {inst1_short} ↔ {inst2_short}: {weight}")
            
            # Instituições mais centrais
            centrality = nx.degree_centrality(G)
            top_central = sorted(centrality.items(), key=lambda x: x[1], reverse=True)[:3]
            print(f"\n🏆 Top 3 instituições mais centrais:")
            for i, (inst, cent) in enumerate(top_central, 1):
                inst_short = inst[:40] + "..." if len(inst) > 40 else inst
                print(f"   {i}. {inst_short}: {cent:.3f}")
            
            return True
        else:
            print(f"⚠️ Nenhuma colaboração encontrada com mínimo de {min_collaboration}")
            return False
            
    except Exception as e:
        print(f"❌ Erro na análise de colaboração institucional: {e}")
        return False

def main():
    """Função principal do teste."""
    print("🚀 TESTANDO NOVAS ANÁLISES DE REDE")
    print("=" * 60)
    
    # Testar coocorrência de palavras-chave
    keyword_success = test_keyword_cooccurrence()
    
    # Testar colaboração institucional
    institution_success = test_institution_collaboration()
    
    print("\n" + "=" * 60)
    print("✅ RESUMO DOS TESTES")
    print(f"🔗 Rede de Coocorrência: {'✅ OK' if keyword_success else '❌ FALHOU'}")
    print(f"🏛️ Rede de Instituições: {'✅ OK' if institution_success else '❌ FALHOU'}")
    
    if keyword_success and institution_success:
        print("\n🎉 TODAS AS NOVAS ANÁLISES ESTÃO FUNCIONANDO!")
        print("🚀 Execute: streamlit run streamlit_bibliometric_app.py")
    else:
        print("\n⚠️ Algumas análises apresentaram problemas")

if __name__ == "__main__":
    main()
